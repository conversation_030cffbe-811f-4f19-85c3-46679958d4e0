#!/usr/bin/env python3
"""
Integration Test for Organization Search Flow

This test verifies the complete search flow from organization level
down to the Jira connector, ensuring all components work together correctly.
"""

import sys
import os
import logging
from datetime import datetime

# Add the modules directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'modules'))

from modules.organisation.service import OrganizationService
from modules.connectors.utilities.constant.schemas import SearchStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_organization_search_flow():
    """Test the complete organization search flow"""
    
    print("=" * 60)
    print("TESTING ORGANIZATION SEARCH FLOW")
    print("=" * 60)
    
    try:
        # Initialize organization service
        org_id = "test-org-123"
        org_service = OrganizationService(org_id)
        
        print(f"\n✓ Organization service initialized for org: {org_id}")
        
        # Test different types of queries
        test_queries = [
            "Find all bugs assigned to <PERSON>",
            "Show me issues in project ABC",
            "List all open tickets",
            "Search for login related problems",
            "Get all user stories for sprint 5"
        ]
        
        print(f"\n📋 Testing {len(test_queries)} different search queries...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- Test {i}: '{query}' ---")
            
            try:
                # Execute search
                start_time = datetime.now()
                response = org_service.search(query)
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                
                # Validate response
                assert response is not None, "Response should not be None"
                assert hasattr(response, 'status'), "Response should have status"
                assert hasattr(response, 'request'), "Response should have request"
                assert hasattr(response, 'aggregated_results'), "Response should have aggregated_results"
                assert hasattr(response, 'connector_responses'), "Response should have connector_responses"

                print(f"  ✓ Status: {response.status.value}")
                print(f"  ✓ Query: {response.request.query}")
                print(f"  ✓ Total Results: {response.total_results}")
                print(f"  ✓ Connectors Searched: {response.connectors_searched}")
                print(f"  ✓ Execution Time: {execution_time:.2f}ms")
                
                # Check connector responses
                if response.connector_responses:
                    for conn_response in response.connector_responses:
                        print(f"    - {conn_response.connector_info.get('source_type', 'unknown')}: "
                              f"{len(conn_response.results)} results")
                        
                        # Validate connector response structure
                        assert hasattr(conn_response, 'status'), "Connector response should have status"
                        assert hasattr(conn_response, 'results'), "Connector response should have results"
                        assert hasattr(conn_response, 'metrics'), "Connector response should have metrics"
                        
                        # Check individual results
                        for result in conn_response.results[:2]:  # Check first 2 results
                            assert hasattr(result, 'id'), "Result should have id"
                            assert hasattr(result, 'title'), "Result should have title"
                            assert hasattr(result, 'source_type'), "Result should have source_type"
                            assert hasattr(result, 'entity_type'), "Result should have entity_type"
                            print(f"      * {result.id}: {result.title[:50]}...")
                
                if response.status == SearchStatus.SUCCESS:
                    print(f"  ✅ Search successful!")
                elif response.status == SearchStatus.NO_RESULTS:
                    print(f"  ⚠️  No results found")
                else:
                    print(f"  ❌ Search failed: {response.status.value}")
                    if response.errors:
                        for error in response.errors:
                            print(f"    Error: {error.error_message}")
                
            except Exception as e:
                print(f"  ❌ Test failed: {str(e)}")
                logger.error(f"Search test failed for query '{query}': {e}", exc_info=True)
        
        print(f"\n🎯 TESTING SPECIFIC FEATURES")
        print("-" * 40)
        
        # Test with filters
        print("\n--- Testing with filters ---")
        response = org_service.search(
            "Find issues",
            filters={"status": "open", "priority": "high"},
            page_size=5
        )
        print(f"  ✓ Filtered search: {response.total_results} results")
        
        # Test with specific connectors
        print("\n--- Testing with specific connectors ---")
        response = org_service.search(
            "Search everything",
            include_connectors=["jira"]
        )
        print(f"  ✓ Jira-only search: {response.total_results} results")
        print(f"  ✓ Connectors used: {response.connectors_searched}")
        
        # Test connector capabilities
        print("\n--- Testing connector capabilities ---")
        available_connectors = org_service.get_available_connectors()
        print(f"  ✓ Available connectors: {len(available_connectors)}")
        for connector in available_connectors:
            print(f"    - {connector['name']}: {connector['description']}")
        
        print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {str(e)}")
        logger.error(f"Critical test failure: {e}", exc_info=True)
        return False


def test_connector_router():
    """Test the connector router functionality"""
    
    print("\n🔀 TESTING CONNECTOR ROUTER")
    print("-" * 40)
    
    try:
        from modules.organisation.connector_router import ConnectorRouter
        from modules.connectors.utilities.constant.schemas import OrganizationSearchRequest
        
        router = ConnectorRouter("test-org")
        
        # Test different query types
        test_cases = [
            ("Find bug PROJ-123", ["jira"]),
            ("Show me documentation about API", ["confluence"]),
            ("List all repositories", ["github"]),
            ("Search for everything", ["jira"])  # Default fallback
        ]
        
        for query, expected_connectors in test_cases:
            request = OrganizationSearchRequest(query=query, org_id="test-org")
            connectors = router.determine_connectors(request)
            
            print(f"  Query: '{query}'")
            print(f"    Expected: {expected_connectors}")
            print(f"    Got: {connectors}")
            
            # Check if at least one expected connector is present
            has_expected = any(conn in connectors for conn in expected_connectors)
            if has_expected:
                print(f"    ✅ Correct routing")
            else:
                print(f"    ⚠️  Unexpected routing (may be fallback)")
        
        print(f"  ✓ Connector router tests completed")
        return True
        
    except Exception as e:
        print(f"  ❌ Connector router test failed: {str(e)}")
        logger.error(f"Connector router test failed: {e}", exc_info=True)
        return False


def main():
    """Run all integration tests"""
    
    print("🚀 STARTING ORGANIZATION SEARCH INTEGRATION TESTS")
    print("=" * 60)
    
    success = True
    
    # Test main search flow
    if not test_organization_search_flow():
        success = False
    
    # Test connector router
    if not test_connector_router():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("The organization search flow is working correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the logs for details.")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
