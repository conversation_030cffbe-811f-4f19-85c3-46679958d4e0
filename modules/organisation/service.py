"""
Organization Service Module

This module provides organization-level services including user management,
settings, permissions, and high-level coordination of organization resources.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from modules.connectors.utilities.constant.schemas import (
    OrganizationSearchRequest,
    OrganizationSearchResponse,
    SearchStatus,
    SearchMetrics,
    SearchError
)
from modules.connectors.search_service import ConnectorSearchService

# Configure logging
logger = logging.getLogger(__name__)


class OrganizationService:
    """
    Main service class for organization-level operations.

    This service handles:
    - Organization settings and configuration
    - User management and permissions
    - High-level search coordination (delegates to ConnectorSearchService)
    - Organization-wide analytics and reporting
    - Integration management
    """

    def __init__(self, org_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the organization service.

        Args:
            org_id: Organization identifier
            config: Optional configuration dictionary
        """
        self.org_id = org_id
        self.config = config or {}

        # Initialize search service for connector operations
        self.search_service = ConnectorSearchService(org_id)

        # Organization-level configuration
        self.org_settings = {
            "name": f"Organization {org_id}",
            "created_at": datetime.now(),
            "search_enabled": True,
            "max_concurrent_searches": 5
        }
        self.org_settings.update(self.config.get("organization", {}))

        logger.info(f"OrganizationService initialized for org_id: {org_id}")
    
    def search(self, query: str, **kwargs) -> OrganizationSearchResponse:
        """
        Main search method that coordinates search across multiple connectors.

        This method delegates the actual search operations to the ConnectorSearchService
        while handling organization-level concerns like permissions, logging, and metrics.

        Args:
            query: Search query string
            **kwargs: Additional search parameters (user_id, filters, page, page_size, etc.)

        Returns:
            OrganizationSearchResponse: Aggregated search results from all connectors
        """
        start_time = datetime.now()

        try:
            # Validate organization-level permissions and settings
            if not self.org_settings.get("search_enabled", True):
                raise ValueError("Search is disabled for this organization")

            # Create standardized search request
            search_request = self._create_search_request(query, **kwargs)
            logger.info(f"Processing search request for org {self.org_id}: '{query}'")

            # Delegate to connector search service
            connector_responses = self.search_service.search_connectors(search_request)

            # Create organization response
            response = OrganizationSearchResponse(
                status=SearchStatus.SUCCESS,
                request=search_request
            )

            # Aggregate results from connectors
            for connector_response in connector_responses:
                response.add_connector_response(connector_response)

            # Finalize response
            response.finalize_response()

            # Calculate overall metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            response.overall_metrics = SearchMetrics(
                execution_time_ms=execution_time,
                total_results_found=response.total_results,
                results_returned=len(response.aggregated_results),
                cache_hit=any(r.metrics and r.metrics.cache_hit for r in response.connector_responses if r.metrics)
            )

            logger.info(f"Search completed in {execution_time:.2f}ms, found {response.total_results} results across {len(response.connectors_searched)} connectors")
            return response

        except Exception as e:
            logger.error(f"Search failed for org {self.org_id}: {str(e)}", exc_info=True)

            # Return error response
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            error_response = OrganizationSearchResponse(
                status=SearchStatus.ERROR,
                request=self._create_search_request(query, **kwargs),
                errors=[SearchError(
                    error_code="ORGANIZATION_SEARCH_ERROR",
                    error_message=str(e),
                    error_type="internal_error"
                )],
                overall_metrics=SearchMetrics(
                    execution_time_ms=execution_time,
                    total_results_found=0,
                    results_returned=0
                )
            )
            return error_response
    
    def _create_search_request(self, query: str, **kwargs) -> OrganizationSearchRequest:
        """Create a standardized search request from parameters."""
        return OrganizationSearchRequest(
            query=query,
            org_id=self.org_id,
            user_id=kwargs.get("user_id"),
            filters=kwargs.get("filters", {}),
            page=kwargs.get("page", 1),
            page_size=kwargs.get("page_size", 10),
            include_connectors=kwargs.get("include_connectors"),
            exclude_connectors=kwargs.get("exclude_connectors"),
            search_options=kwargs.get("search_options", {}),
            enable_parallel_search=kwargs.get("enable_parallel_search", False)
        )
    
    # Organization-wide feature methods

    def get_organization_info(self) -> Dict[str, Any]:
        """
        Get organization information and settings.

        Returns:
            Organization information dictionary
        """
        return {
            "org_id": self.org_id,
            "settings": self.org_settings,
            "search_enabled": self.org_settings.get("search_enabled", True),
            "available_connectors": self.get_available_connectors(),
            "search_statistics": self._get_search_statistics()
        }

    def get_available_connectors(self) -> List[Dict[str, Any]]:
        """
        Get list of available connectors for this organization.

        Returns:
            List of connector metadata dictionaries
        """
        # Delegate to search service for connector information
        return self.search_service.get_available_connectors()

    def add_connector(self, connector_type: str, connector_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new connector to this organization.

        Args:
            connector_type: Type of connector to add
            connector_config: Configuration for the connector

        Returns:
            Result of the add operation
        """
        # TODO: Implement connector addition logic with database persistence
        logger.info(f"Adding connector {connector_type} to organization {self.org_id} with config keys: {list(connector_config.keys())}")
        return {"status": "success", "connector_type": connector_type, "config_provided": bool(connector_config)}

    def update_organization_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update organization settings.

        Args:
            settings: Settings to update

        Returns:
            Updated settings
        """
        self.org_settings.update(settings)
        logger.info(f"Updated organization settings for {self.org_id}")
        return self.org_settings

    def _get_search_statistics(self) -> Dict[str, Any]:
        """
        Get search statistics for this organization.

        Returns:
            Search statistics dictionary
        """
        # TODO: Implement actual statistics from database
        return {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "average_response_time_ms": 0,
            "most_used_connectors": []
        }
