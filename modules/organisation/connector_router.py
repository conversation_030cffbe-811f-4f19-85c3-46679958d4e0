"""
Connector Router Module

This module provides intelligent routing of search queries to appropriate connectors
based on query intent, content analysis, and organization configuration.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from modules.connectors.utilities.constant.schemas import OrganizationSearchRequest

# Configure logging
logger = logging.getLogger(__name__)


class QueryIntent(Enum):
    """Enumeration of different query intents"""
    TASK_MANAGEMENT = "task_management"  # <PERSON><PERSON>, Asana, etc.
    DOCUMENTATION = "documentation"      # Confluence, Notion, etc.
    CODE_REPOSITORY = "code_repository"  # GitHub, GitLab, etc.
    COMMUNICATION = "communication"      # Slack, Teams, etc.
    FILE_STORAGE = "file_storage"       # Google Drive, SharePoint, etc.
    GENERAL = "general"                 # When intent is unclear


@dataclass
class ConnectorCapability:
    """Represents a connector's capabilities and metadata"""
    name: str
    type: str  # structured/unstructured
    category: str
    keywords: List[str]
    patterns: List[str]
    priority: int = 1  # Higher number = higher priority
    is_active: bool = True


class ConnectorRouter:
    """
    Intelligent router that determines which connectors to use for a given search query.
    
    This router analyzes query intent, content, and organization configuration to
    determine the most appropriate connectors for executing the search.
    """
    
    def __init__(self, org_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the connector router.
        
        Args:
            org_id: Organization identifier
            config: Optional configuration dictionary
        """
        self.org_id = org_id
        self.config = config or {}
        
        # Initialize connector capabilities
        self.connector_capabilities = self._initialize_connector_capabilities()
        
        # Query intent patterns
        self.intent_patterns = self._initialize_intent_patterns()
        
        logger.info(f"ConnectorRouter initialized for org_id: {org_id}")
    
    def determine_connectors(self, request: OrganizationSearchRequest) -> List[str]:
        """
        Determine which connectors to search based on the request.
        
        Args:
            request: The search request containing query and filters
            
        Returns:
            List of connector names to search, ordered by priority
        """
        query = request.query.lower()
        
        # Check for explicit connector inclusion/exclusion
        if request.include_connectors:
            logger.info(f"Using explicitly included connectors: {request.include_connectors}")
            return self._filter_active_connectors(request.include_connectors)
        
        # Analyze query intent
        detected_intents = self._analyze_query_intent(query)
        logger.info(f"Detected query intents: {detected_intents}")
        
        # Get connectors based on intent
        candidate_connectors = self._get_connectors_for_intents(detected_intents)
        
        # Apply filters and exclusions
        if request.exclude_connectors:
            candidate_connectors = [c for c in candidate_connectors if c not in request.exclude_connectors]
        
        # Filter by active connectors for this organization
        active_connectors = self._filter_active_connectors(candidate_connectors)
        
        # If no specific connectors found and fallback is enabled, use all active connectors
        if not active_connectors and self.config.get("fallback_to_all_connectors", False):
            active_connectors = self._get_all_active_connectors()
            logger.info("No specific connectors found, using all active connectors as fallback")
        
        # Default to Jira if no connectors determined (for now)
        if not active_connectors:
            active_connectors = ["jira"]
            logger.info("No connectors determined, defaulting to Jira")
        
        logger.info(f"Final connector selection: {active_connectors}")
        return active_connectors
    
    def _initialize_connector_capabilities(self) -> Dict[str, ConnectorCapability]:
        """Initialize the connector capabilities database."""
        return {
            "jira": ConnectorCapability(
                name="jira",
                type="structured",
                category="task_management",
                keywords=[
                    "issue", "ticket", "bug", "task", "story", "epic", "sprint",
                    "project", "assignee", "status", "priority", "jira",
                    "assigned", "created", "updated", "resolved", "closed"
                ],
                patterns=[
                    r"\b(issue|ticket|bug|task)\b",
                    r"\b(assigned\s+to|created\s+by|updated\s+by)\b",
                    r"\b(project\s+\w+|sprint\s+\w+)\b",
                    r"\b(status|priority|resolution)\b",
                    r"\b[A-Z]+-\d+\b"  # Jira issue key pattern
                ],
                priority=2
            ),
            "confluence": ConnectorCapability(
                name="confluence",
                type="unstructured",
                category="documentation",
                keywords=[
                    "document", "page", "wiki", "documentation", "confluence",
                    "article", "guide", "manual", "spec", "specification"
                ],
                patterns=[
                    r"\b(document|documentation|wiki|page)\b",
                    r"\b(guide|manual|spec|specification)\b",
                    r"\b(how\s+to|tutorial|instructions)\b"
                ],
                priority=1
            ),
            "github": ConnectorCapability(
                name="github",
                type="structured",
                category="code_repository",
                keywords=[
                    "repository", "repo", "code", "commit", "pull request", "pr",
                    "branch", "github", "git", "source", "file", "class", "function"
                ],
                patterns=[
                    r"\b(repository|repo|code|commit)\b",
                    r"\b(pull\s+request|pr|branch)\b",
                    r"\b(class|function|method|variable)\b",
                    r"\b(\.py|\.js|\.java|\.cpp|\.c)\b"  # File extensions
                ],
                priority=1
            )
        }
    
    def _initialize_intent_patterns(self) -> Dict[QueryIntent, List[str]]:
        """Initialize patterns for detecting query intent."""
        return {
            QueryIntent.TASK_MANAGEMENT: [
                r"\b(issue|ticket|bug|task|story|epic|sprint|project)\b",
                r"\b(assigned|assignee|status|priority|resolution)\b",
                r"\b(created|updated|resolved|closed|reopened)\b",
                r"\b[A-Z]+-\d+\b"  # Issue key pattern
            ],
            QueryIntent.DOCUMENTATION: [
                r"\b(document|documentation|wiki|page|article)\b",
                r"\b(guide|manual|spec|specification|tutorial)\b",
                r"\b(how\s+to|instructions|procedure)\b"
            ],
            QueryIntent.CODE_REPOSITORY: [
                r"\b(repository|repo|code|commit|pull\s+request|pr)\b",
                r"\b(branch|merge|git|github|source)\b",
                r"\b(class|function|method|variable|file)\b",
                r"\b\.(py|js|java|cpp|c|html|css|sql)\b"
            ],
            QueryIntent.COMMUNICATION: [
                r"\b(message|chat|conversation|discussion)\b",
                r"\b(slack|teams|discord|email)\b",
                r"\b(channel|thread|mention)\b"
            ],
            QueryIntent.FILE_STORAGE: [
                r"\b(file|folder|directory|drive|storage)\b",
                r"\b(upload|download|share|sync)\b",
                r"\b(google\s+drive|sharepoint|dropbox)\b"
            ]
        }
    
    def _analyze_query_intent(self, query: str) -> List[QueryIntent]:
        """
        Analyze the query to determine its intent.
        
        Args:
            query: The search query (lowercase)
            
        Returns:
            List of detected intents, ordered by confidence
        """
        detected_intents = []
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query, re.IGNORECASE))
                score += matches
            
            if score > 0:
                intent_scores[intent] = score
        
        # Sort by score (highest first)
        sorted_intents = sorted(intent_scores.items(), key=lambda x: x[1], reverse=True)
        detected_intents = [intent for intent, score in sorted_intents]
        
        # If no specific intent detected, use general
        if not detected_intents:
            detected_intents = [QueryIntent.GENERAL]
        
        return detected_intents
    
    def _get_connectors_for_intents(self, intents: List[QueryIntent]) -> List[str]:
        """
        Get connectors that match the detected intents.
        
        Args:
            intents: List of detected query intents
            
        Returns:
            List of connector names, ordered by priority
        """
        connector_scores = {}
        
        for intent in intents:
            # Map intents to connector categories
            if intent == QueryIntent.TASK_MANAGEMENT:
                target_category = "task_management"
            elif intent == QueryIntent.DOCUMENTATION:
                target_category = "documentation"
            elif intent == QueryIntent.CODE_REPOSITORY:
                target_category = "code_repository"
            elif intent == QueryIntent.COMMUNICATION:
                target_category = "communication"
            elif intent == QueryIntent.FILE_STORAGE:
                target_category = "file_storage"
            else:
                # For general intent, consider all connectors
                target_category = None
            
            # Score connectors based on category match
            for name, capability in self.connector_capabilities.items():
                if not capability.is_active:
                    continue
                
                if target_category is None or capability.category == target_category:
                    current_score = connector_scores.get(name, 0)
                    connector_scores[name] = current_score + capability.priority
        
        # Sort by score (highest first)
        sorted_connectors = sorted(connector_scores.items(), key=lambda x: x[1], reverse=True)
        return [name for name, score in sorted_connectors]
    
    def _filter_active_connectors(self, connector_names: List[str]) -> List[str]:
        """
        Filter connector names to only include active ones for this organization.
        
        Args:
            connector_names: List of connector names to filter
            
        Returns:
            List of active connector names
        """
        # TODO: Implement actual database lookup for organization's active connectors
        # For now, return all connectors that exist in our capabilities
        active_connectors = []
        for name in connector_names:
            if name in self.connector_capabilities and self.connector_capabilities[name].is_active:
                active_connectors.append(name)
        
        return active_connectors
    
    def _get_all_active_connectors(self) -> List[str]:
        """Get all active connectors for this organization."""
        return [
            name for name, capability in self.connector_capabilities.items()
            if capability.is_active
        ]
    
    def add_connector_capability(self, capability: ConnectorCapability):
        """Add a new connector capability to the router."""
        self.connector_capabilities[capability.name] = capability
        logger.info(f"Added connector capability: {capability.name}")
    
    def update_connector_status(self, connector_name: str, is_active: bool):
        """Update the active status of a connector."""
        if connector_name in self.connector_capabilities:
            self.connector_capabilities[connector_name].is_active = is_active
            logger.info(f"Updated connector {connector_name} status to: {is_active}")
        else:
            logger.warning(f"Connector {connector_name} not found in capabilities")
