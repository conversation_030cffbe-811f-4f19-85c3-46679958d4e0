"""
Connector Search Service

This module handles all connector-specific search operations, providing a clean
interface between the organization service and individual connectors.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from modules.connectors.utilities.constant.schemas import (
    ConnectorSearchResponse, 
    OrganizationSearchRequest,
    SearchStatus,
    SearchError,
    SearchMetrics
)
from modules.organisation.connector_router import ConnectorRouter

logger = logging.getLogger(__name__)


class ConnectorSearchService:
    """
    Service responsible for coordinating search operations across multiple connectors.
    
    This service:
    - Manages connector routing and selection
    - Executes searches across multiple connectors
    - Handles connector-specific configurations
    - Provides unified error handling for connector operations
    """
    
    def __init__(self, org_id: str):
        """
        Initialize the connector search service.
        
        Args:
            org_id: Organization identifier
        """
        self.org_id = org_id
        self.connector_router = ConnectorRouter(org_id)
        logger.info(f"ConnectorSearchService initialized for org_id: {org_id}")
    
    def search_connectors(
        self, 
        request: OrganizationSearchRequest
    ) -> List[ConnectorSearchResponse]:
        """
        Execute search across multiple connectors based on the request.
        
        Args:
            request: Organization search request containing query and parameters
            
        Returns:
            List of connector search responses
        """
        start_time = datetime.now()
        
        # Determine target connectors
        target_connectors = self._determine_target_connectors(request)
        logger.info(f"Target connectors for search: {target_connectors}")
        
        # Execute searches
        if request.enable_parallel_search and len(target_connectors) > 1:
            connector_responses = self._execute_parallel_search(request, target_connectors)
        else:
            logger.info("Parallel search not yet implemented, falling back to sequential")
            connector_responses = self._execute_sequential_search(request, target_connectors)
        
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        successful_connectors = [resp.connector_info.get('source_type', 'unknown') 
                               for resp in connector_responses 
                               if resp.status == SearchStatus.SUCCESS]
        
        logger.info(f"Connector search completed in {execution_time:.2f}ms, "
                   f"successful connectors: {successful_connectors}")
        
        return connector_responses
    
    def _determine_target_connectors(self, request: OrganizationSearchRequest) -> List[str]:
        """
        Determine which connectors should be used for the search.
        
        Args:
            request: Organization search request
            
        Returns:
            List of connector names to search
        """
        return self.connector_router.determine_connectors(request)
    
    def _execute_sequential_search(
        self, 
        request: OrganizationSearchRequest, 
        target_connectors: List[str]
    ) -> List[ConnectorSearchResponse]:
        """
        Execute search across connectors sequentially.
        
        Args:
            request: Organization search request
            target_connectors: List of connector names to search
            
        Returns:
            List of connector search responses
        """
        connector_responses = []
        
        for connector_name in target_connectors:
            try:
                logger.info(f"Searching connector: {connector_name}")
                response = self._search_single_connector(connector_name, request)
                connector_responses.append(response)
                
            except Exception as e:
                logger.error(f"Search failed for connector {connector_name}: {e}")
                # Create error response for failed connector
                error_response = ConnectorSearchResponse(
                    status=SearchStatus.ERROR,
                    query=request.query,
                    results=[],
                    total_count=0,
                    connector_info={
                        'source_type': connector_name,
                        'name': connector_name.title(),
                        'version': '1.0.0'
                    },
                    error=SearchError(
                        error_code="CONNECTOR_ERROR",
                        error_type="execution_error",
                        error_message=str(e),
                        details={'connector': connector_name}
                    )
                )
                connector_responses.append(error_response)
        
        return connector_responses
    
    def _execute_parallel_search(
        self, 
        request: OrganizationSearchRequest, 
        target_connectors: List[str]
    ) -> List[ConnectorSearchResponse]:
        """
        Execute search across connectors in parallel.
        
        Args:
            request: Organization search request
            target_connectors: List of connector names to search
            
        Returns:
            List of connector search responses
        """
        # TODO: Implement parallel search using asyncio or threading
        logger.warning("Parallel search not yet implemented, falling back to sequential")
        return self._execute_sequential_search(request, target_connectors)
    
    def _search_single_connector(
        self, 
        connector_name: str, 
        request: OrganizationSearchRequest
    ) -> ConnectorSearchResponse:
        """
        Execute search on a single connector.
        
        Args:
            connector_name: Name of the connector to search
            request: Organization search request
            
        Returns:
            Connector search response
        """
        # Get connector configuration
        connector_config = self._get_connector_config(connector_name)
        
        # Get connector instance (simplified for current implementation)
        if connector_name.lower() == "jira":
            from modules.connectors.handlers.Jira.service import JiraConnectorService
            connector_instance = JiraConnectorService(config=connector_config)
        else:
            raise ValueError(f"Connector '{connector_name}' not supported in current implementation")
        
        # Execute search on the connector
        return connector_instance.search(request.query)
    
    def _get_connector_config(self, connector_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific connector.
        
        Args:
            connector_name: Name of the connector
            
        Returns:
            Connector configuration dictionary
        """
        # TODO: Load from database or configuration service
        # For now, return default configurations
        
        default_configs = {
            "jira": {
                "jira_url": "https://your-org.atlassian.net",
                "username": "api_user",
                "password": "api_token",
                "max_results": 50,
                "enable_caching": True,
                "enable_query_logging": True
            },
            "confluence": {
                "base_url": "https://your-org.atlassian.net/wiki",
                "username": "api_user",
                "password": "api_token",
                "max_results": 50
            },
            "github": {
                "base_url": "https://api.github.com",
                "token": "github_token",
                "organization": "your-org",
                "max_results": 50
            }
        }
        
        return default_configs.get(connector_name.lower(), {})
    
    def get_available_connectors(self) -> List[Dict[str, str]]:
        """
        Get list of available connectors for this organization.
        
        Returns:
            List of connector information dictionaries
        """
        # TODO: Load from database based on organization configuration
        return [
            {
                "name": "jira",
                "display_name": "Jira",
                "description": "Jira issue tracking system",
                "status": "active",
                "capabilities": ["search", "filter", "advanced_query"]
            },
            {
                "name": "confluence",
                "display_name": "Confluence",
                "description": "Confluence documentation system",
                "status": "inactive",
                "capabilities": ["search", "filter"]
            },
            {
                "name": "github",
                "display_name": "GitHub",
                "description": "GitHub code repository",
                "status": "inactive",
                "capabilities": ["search", "filter", "code_search"]
            }
        ]
    
    def validate_connector_health(self, connector_name: str) -> Dict[str, Any]:
        """
        Check the health status of a specific connector.
        
        Args:
            connector_name: Name of the connector to check
            
        Returns:
            Health status information
        """
        try:
            # TODO: Implement actual health checks
            return {
                "connector": connector_name,
                "status": "healthy",
                "last_check": datetime.now().isoformat(),
                "response_time_ms": 50,
                "error_rate": 0.0
            }
        except Exception as e:
            return {
                "connector": connector_name,
                "status": "unhealthy",
                "last_check": datetime.now().isoformat(),
                "error": str(e)
            }
