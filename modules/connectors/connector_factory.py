import importlib
from typing import Dict, Type
# Assuming you have a way to query your DB
from app.database import get_database_session 
from app.connectors.base import BaseConnector

class ConnectorFactory:
    _connector_classes: Dict[str, Type[BaseConnector]] = {}

    def __init__(self):
        # The loading process happens only once when the factory is first created.
        if not self._connector_classes:
            self._load_connectors_from_db()

    def _load_connectors_from_db(self):
        """
        Loads connector classes based on what's in the database.
        This is the magic that connects your DB registry to your code.
        """
        print("--- Initializing ConnectorFactory: Loading active connectors ---")
        # 1. Query your database for the list of available connectors
        db_session = get_database_session()
        # Pretend this function queries 'SELECT source_type FROM connectors WHERE is_active=true'
        active_connectors = db_session.get_active_connector_types() 
        db_session.close()

        for source_type in active_connectors:
            try:
                # 2. Dynamically construct the module path and import it
                # e.g., 'app.connectors.gdrive.service'
                module_path = f"app.connectors.{source_type}.service"
                module = importlib.import_module(module_path)

                # 3. Find the class within the module that inherits from BaseConnector
                for attribute_name in dir(module):
                    attribute = getattr(module, attribute_name)
                    if isinstance(attribute, type) and issubclass(attribute, BaseConnector) and attribute is not BaseConnector:
                        # 4. Store the class in our dictionary, mapping the type to the class itself
                        self._connector_classes[source_type] = attribute
                        print(f"  [+] Registered connector: '{source_type}'")
                        break
            except ImportError:
                print(f"  [!] WARNING: Connector '{source_type}' is active in DB but code not found.")
            except Exception as e:
                print(f"  [!] ERROR: Failed to load connector '{source_type}'. Reason: {e}")

    def get_connector(self, source_type: str, config: Dict[str, Any]) -> BaseConnector:
        """
        The main public method. Gets the appropriate connector class,
        instantiates it with the given config, and returns the instance.
        """
        connector_class = self._connector_classes.get(source_type)
        if not connector_class:
            raise ValueError(f"Connector '{source_type}' is not registered or failed to load.")
        
        # Return an *instance* of the class, initialized with its config
        return connector_class(config=config)

# Create a single, global instance of the factory to be used everywhere
connector_factory = ConnectorFactory()