from abc import ABC, abstractmethod
from typing import Any, Dict, List, Iterator

class BaseConnector(ABC):
    """
    Defines the standard interface for all data source connectors
    as per the Unified Connector Architecture Design.
    """

    CONNECTOR_TYPE: str  # "structured" or "unstructured"

    @abstractmethod
    def connect(self) -> Any:
        """Establish and return client/connection"""
        pass

    @abstractmethod
    def get_connector(self) -> dict:
        """Returns metadata about the connector"""
        pass

    @abstractmethod
    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """Pulls all data, preferably with pagination as an iterator."""
        pass

    @abstractmethod
    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """Fetch a single entity by its ID."""
        pass

    @abstractmethod
    def sync(self):
        """Perform a full sync of the data source."""
        pass

    @abstractmethod
    def sync_by_id(self, id: str):
        """Perform a partial sync for a single entity."""
        pass

    @abstractmethod
    def store_context(self, data: Any):
        """Stores both context and embedding for given data."""
        pass

    @abstractmethod
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search for data within the source."""
        pass