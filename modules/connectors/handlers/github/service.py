"""
GitHub Connector Service

This module implements the main GitHubConnectorService class that inherits from BaseConnector
and provides comprehensive GitHub integration functionality.
"""

import re
import logging
import hashlib
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Iterator, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from functools import lru_cache

from modules.connectors.base import BaseConnector
from modules.connectors.handlers.github.constants.entities import EntityType, get_all_entity_types
from modules.connectors.handlers.github.constants.relationships import GitHubRelationshipType, get_all_relationship_types
from modules.connectors.handlers.github.connection import GitHubConnection
from modules.connectors.handlers.github.neo4j_handler import GitHubNeo4jHandler
from modules.connectors.handlers.github.search_implementation import GitHubKnowledgeGraphSearch, GitHubAPISearch, SearchContext, SearchType
from modules.connectors.handlers.github.schema import (
    GitHubConfig, GitHubSearchQuery, GitHubSyncRequest, GitHubConnectorResponse,
    GitHubUser, GitHubRepository, GitHubIssue, GitHubPullRequest, GitHubCommit, GitHubWorkflow
)
from modules.connectors.utilities.constant.schemas import (
    ConnectorSearchResponse,
    SearchResultItem,
    SearchStatus,
    SearchMetrics,
    SearchError
)

# Configure logging
logger = logging.getLogger(__name__)

# Configuration with dummy credentials
DEFAULT_CONFIG = {
    # GitHub API Configuration
    'api_base_url': 'https://api.github.com',
    'graphql_url': 'https://api.github.com/graphql',
    'token': 'ghp_placeholder_token_here',  # Personal Access Token placeholder
    'webhook_secret': 'webhook_secret_placeholder',
    'rate_limit_per_hour': 5000,
    'timeout_seconds': 30,
    
    # Database Configuration (Dummy)
    'postgres': {
        'host': 'localhost',
        'port': 5432,
        'database': 'github_connector_db',
        'username': 'github_user',
        'password': 'dummy_postgres_password_123'
    },
    
    # Pinecone Configuration (Dummy)
    'pinecone': {
        'api_key': 'dummy_pinecone_api_key_12345',
        'environment': 'us-west1-gcp',
        'index_name': 'github-connector-index',
        'dimension': 1536
    }
}

@dataclass
class SearchConfig:
    """Configuration for GitHub search functionality"""
    max_results: int = 100
    default_limit: int = 30
    cache_ttl_seconds: int = 300  # 5 minutes
    enable_fuzzy_search: bool = True
    fuzzy_threshold: float = 0.8
    enable_caching: bool = True
    query_timeout_seconds: int = 30
    max_query_length: int = 1000
    enable_query_logging: bool = True

class QueryType(Enum):
    """Enumeration of supported GitHub query types"""
    REPOSITORY_SEARCH = "repository_search"
    ISSUE_SEARCH = "issue_search"
    PULL_REQUEST_SEARCH = "pull_request_search"
    USER_SEARCH = "user_search"
    COMMIT_SEARCH = "commit_search"
    CODE_SEARCH = "code_search"
    ORGANIZATION_SEARCH = "organization_search"
    WORKFLOW_SEARCH = "workflow_search"
    GENERIC_SEARCH = "generic_search"
    DATE_FILTERED = "date_filtered"

@dataclass
class ParsedQuery:
    """Structured representation of a parsed GitHub search query"""
    query_type: QueryType
    keywords: List[str]
    entity_filters: Dict[str, str]
    date_filter: Optional[datetime]
    repository_filter: Optional[str]
    user_filter: Optional[str]
    language_filter: Optional[str]
    limit: int
    raw_query: str
    confidence_score: float

class GitHubConnectorService(BaseConnector):
    """
    Comprehensive GitHub connector service implementation.
    
    This service provides full integration with GitHub API including repositories,
    issues, pull requests, commits, users, organizations, workflows, and more.
    """
    
    CONNECTOR_TYPE = "structured"
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize GitHub connector service.
        
        Args:
            config: Configuration dictionary, uses defaults if not provided
        """
        self.config = {**DEFAULT_CONFIG, **(config or {})}
        self.github_config = GitHubConfig(**self.config)
        self.search_config = SearchConfig()
        
        # Initialize connections
        self.connection = None
        self.neo4j_handler = None
        self._connected = False
        
        # Data limits for testing
        self.data_limits = {
            'repositories': 2,
            'issues': 5,
            'users': 3,
            'organizations': 2,
            'commits': 5,
            'pull_requests': 3
        }

        # Store entities for relationship creation after all entities are stored
        self._pending_relationships = []

        # Connector metadata
        self.source_type = "github"
        self.connector_name = "GitHub"
        self.version = "1.0.0"
        
        logger.info(f"Initialized {self.connector_name} connector v{self.version}")

    def connect(self) -> Any:
        """
        Establish connection to GitHub API and Neo4j database.
        
        Returns:
            GitHubConnection: Connected GitHub API client
        """
        try:
            logger.info("Connecting to GitHub API and Neo4j database...")
            
            # Connect to GitHub API
            self.connection = GitHubConnection(self.config)
            
            if not self.connection.connect():
                raise ConnectionError("Failed to establish GitHub API connection")
            
            # Connect to Neo4j
            self.neo4j_handler = GitHubNeo4jHandler()
            if not self.neo4j_handler.connect():
                raise ConnectionError("Failed to establish Neo4j connection")
            
            self._connected = True
            logger.info("Successfully connected to GitHub API and Neo4j database")
            return self.connection
                
        except Exception as e:
            logger.error(f"Connection failed: {str(e)}")
            self._connected = False
            # Clean up partial connections
            if self.connection:
                self.connection.close()
            if self.neo4j_handler:
                self.neo4j_handler.close()
            raise

    def get_connector(self) -> dict:
        """
        Returns metadata about the GitHub connector.
        
        Returns:
            dict: Connector metadata information
        """
        return {
            "source_type": self.source_type,
            "name": self.connector_name,
            "version": self.version,
            "connector_type": self.CONNECTOR_TYPE,
            "description": "Comprehensive GitHub connector for repositories, issues, pull requests, commits, users, organizations, workflows, and more.",
            "supported_entities": list(get_all_entity_types()),
            "supported_relationships": list(get_all_relationship_types()),
            "capabilities": [
                "repository_management",
                "issue_tracking",
                "pull_request_management",
                "commit_history",
                "user_management",
                "organization_management",
                "workflow_automation",
                "code_search",
                "advanced_search",
                "real_time_sync",
                "webhook_support"
            ],
            "api_endpoints": {
                "rest_api": self.config.get('api_base_url'),
                "graphql_api": self.config.get('graphql_url')
            },
            "rate_limits": {
                "per_hour": self.config.get('rate_limit_per_hour', 5000)
            },
            "authentication": "personal_access_token",
            "connected": self._connected,
            "last_sync": None  # Will be updated during sync operations
        }

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """
        Pulls GitHub data with minimal limits for testing.
        
        Yields:
            Dict[str, Any]: GitHub entity data with relationships
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info("Starting GitHub data fetch with minimal limits...")
            
            # Store fetched data for relationship building
            repositories = []
            users = []
            organizations = []
            
            # Fetch repositories first (they're the foundation)
            logger.info(f"Fetching up to {self.data_limits['repositories']} repositories...")
            for repo_data in self._fetch_repositories():
                repositories.append(repo_data)
                yield repo_data
            
            # Fetch users and organizations
            logger.info(f"Fetching up to {self.data_limits['users']} users...")
            for user_data in self._fetch_users():
                users.append(user_data)
                yield user_data
            
            logger.info(f"Fetching up to {self.data_limits['organizations']} organizations...")
            for org_data in self._fetch_organizations():
                organizations.append(org_data)
                yield org_data
            
            # Fetch repository-specific data with relationships
            logger.info(f"Fetching up to {self.data_limits['issues']} issues per repository...")
            for repo in repositories[:self.data_limits['repositories']]:
                for issue_data in self._fetch_issues_for_repository(repo['github_id']):
                    # Add relationship information
                    issue_data['relationships'] = [
                        {
                            'from_node_type': 'GitHubRepository',
                            'from_node_id': str(repo['github_id']),
                            'to_node_type': 'GitHubIssue',
                            'to_node_id': str(issue_data['github_id']),
                            'relationship_type': 'HAS_ISSUE'
                        }
                    ]
                    yield issue_data
            
            logger.info("Completed GitHub data fetch with minimal limits")
            
        except Exception as e:
            logger.error(f"Error during GitHub data fetch: {str(e)}")
            raise

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a single GitHub entity by its ID.
        
        Args:
            id: Entity identifier (format: entity_type:entity_id)
            
        Returns:
            Dict[str, Any]: Entity data
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            # Parse entity type and ID
            if ':' not in id:
                raise ValueError("ID must be in format 'entity_type:entity_id'")
            
            entity_type, entity_id = id.split(':', 1)
            
            logger.info(f"Fetching GitHub {entity_type} with ID: {entity_id}")
            
            # Route to appropriate fetch method
            if entity_type.lower() == 'repository':
                return self._fetch_repository_by_id(entity_id)
            elif entity_type.lower() == 'user':
                return self._fetch_user_by_id(entity_id)
            elif entity_type.lower() == 'organization':
                return self._fetch_organization_by_id(entity_id)
            else:
                raise ValueError(f"Unsupported entity type: {entity_type}")
                
        except Exception as e:
            logger.error(f"Error fetching GitHub entity by ID {id}: {str(e)}")
            raise

    def sync(self):
        """
        Perform a full synchronization of GitHub data.
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info("Starting full GitHub synchronization...")
            start_time = datetime.now()
            
            # Track sync statistics
            sync_stats = {
                'repositories': 0,
                'users': 0,
                'organizations': 0,
                'issues': 0,
                'errors': []
            }
            
            # Perform full data fetch and store
            for entity_data in self.fetch_data():
                try:
                    # Store context for each entity
                    self.store_context(entity_data)
                    
                    # Update statistics
                    entity_type = entity_data.get('entity_type', 'unknown')
                    if entity_type in sync_stats:
                        sync_stats[entity_type] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to sync entity {entity_data.get('id', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    sync_stats['errors'].append(error_msg)
            
            # Calculate sync duration
            sync_duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"GitHub full sync completed in {sync_duration:.2f} seconds")
            logger.info(f"Sync statistics: {json.dumps(sync_stats, indent=2)}")
            
        except Exception as e:
            logger.error(f"GitHub full sync failed: {str(e)}")
            raise

    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single GitHub entity.
        
        Args:
            id: Entity identifier (format: entity_type:entity_id)
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info(f"Starting partial GitHub sync for entity: {id}")
            
            # Fetch the specific entity
            entity_data = self.fetch_data_by_id(id)
            
            # Store the entity context
            self.store_context(entity_data)
            
            logger.info(f"Successfully synced GitHub entity: {id}")
            
        except Exception as e:
            logger.error(f"GitHub partial sync failed for {id}: {str(e)}")
            raise

    def store_context(self, data: Any):
        """
        Stores GitHub data as nodes and relationships in Neo4j knowledge graph.
        
        Args:
            data: GitHub entity data to store
        """
        try:
            if not isinstance(data, dict):
                logger.warning(f"Invalid data type for storage: {type(data)}")
                return
            
            if not self.neo4j_handler or not self.neo4j_handler._connected:
                logger.error("Neo4j handler not connected")
                return
            
            entity_type = data.get('entity_type', 'unknown')
            entity_id = data.get('github_id', 'unknown')
            
            logger.debug(f"Storing GitHub {entity_type} in Neo4j: {entity_id}")
            
            # Store the entity in Neo4j knowledge graph
            success = self.neo4j_handler.store_github_entity(data)
            
            if success:
                logger.info(f"Successfully stored {entity_type}:{entity_id} in Neo4j knowledge graph")
                
                # Queue ownership relationships for repositories (create after all entities are stored)
                if entity_type == 'repository' and 'owner' in data:
                    owner_data = data['owner']
                    owner_type = 'GitHubOrganization' if owner_data.get('type') == 'Organization' else 'GitHubUser'

                    # Queue owner relationship for later creation
                    self._pending_relationships.append({
                        'from_type': owner_type,
                        'from_id': str(owner_data['id']),
                        'to_type': 'GitHubRepository',
                        'to_id': str(entity_id),
                        'relationship_type': 'OWNS_REPOSITORY',
                        'properties': {'created_at': datetime.now().isoformat()}
                    })

                    logger.debug(f"Queued ownership relationship: {owner_type}({owner_data['id']}) -> GitHubRepository({entity_id})")
                
                # Queue issue relationships (create after all entities are stored)
                elif entity_type == 'issue':
                    # Queue repository-issue relationship from the relationships data
                    if 'relationships' in data:
                        for rel in data['relationships']:
                            self._pending_relationships.append({
                                'from_type': rel['from_node_type'],
                                'from_id': rel['from_node_id'],
                                'to_type': rel['to_node_type'],
                                'to_id': rel['to_node_id'],
                                'relationship_type': rel['relationship_type'],
                                'properties': {'created_at': data.get('created_at', datetime.now().isoformat())}
                            })
                            logger.debug(f"Queued relationship: {rel['from_node_type']}({rel['from_node_id']}) -> {rel['to_node_type']}({rel['to_node_id']})")

                    # Queue user-issue relationships
                    if 'user' in data:
                        user_data = data['user']

                        # Queue creator relationship
                        self._pending_relationships.append({
                            'from_type': 'GitHubUser',
                            'from_id': str(user_data['id']),
                            'to_type': 'GitHubIssue',
                            'to_id': str(entity_id),
                            'relationship_type': 'CREATED_BY',
                            'properties': {'created_at': data.get('created_at')}
                        })

                        logger.debug(f"Queued creator relationship: GitHubUser({user_data['id']}) -> GitHubIssue({entity_id})")

                    # Queue assignee relationships
                    if 'assignees' in data and data['assignees']:
                        for assignee in data['assignees']:
                            self._pending_relationships.append({
                                'from_type': 'GitHubUser',
                                'from_id': str(assignee['id']),
                                'to_type': 'GitHubIssue',
                                'to_id': str(entity_id),
                                'relationship_type': 'ASSIGNED_TO',
                                'properties': {'assigned_at': datetime.now().isoformat()}
                            })

                            logger.debug(f"Queued assignment relationship: GitHubUser({assignee['id']}) -> GitHubIssue({entity_id})")
                
            else:
                logger.error(f"Failed to store {entity_type}:{entity_id} in Neo4j")

        except Exception as e:
            logger.error(f"Failed to store context for GitHub data: {str(e)}")
            raise

    def create_pending_relationships(self):
        """
        Create all pending relationships after all entities have been stored.
        """
        try:
            logger.info(f"Creating {len(self._pending_relationships)} pending relationships...")

            for rel in self._pending_relationships:
                try:
                    self.neo4j_handler.create_relationship(
                        rel['from_type'],
                        rel['from_id'],
                        rel['to_type'],
                        rel['to_id'],
                        rel['relationship_type'],
                        rel['properties']
                    )
                    logger.debug(f"Created relationship: {rel['from_type']}({rel['from_id']}) -[{rel['relationship_type']}]-> {rel['to_type']}({rel['to_id']})")
                except Exception as e:
                    logger.error(f"Failed to create relationship {rel}: {str(e)}")

            # Clear pending relationships
            self._pending_relationships.clear()
            logger.info("Completed creating pending relationships")

        except Exception as e:
            logger.error(f"Error creating pending relationships: {str(e)}")
            raise

    def search(self, query: str, limit: int = 10, search_type: str = "hybrid") -> ConnectorSearchResponse:
        """
        Enhanced GitHub Knowledge Graph Search
        
        Performs intelligent search across GitHub entities stored in Neo4j knowledge graph
        with fallback to GitHub API for real-time data.
        
        Args:
            query: Natural language search query
            limit: Maximum number of results to return
            search_type: Type of search ("hybrid", "entity", "relationship", "traversal")
            
        Returns:
            ConnectorSearchResponse: Standardized search response
        """
        try:
            start_time = datetime.now()
            logger.info(f"Starting GitHub search: '{query}' (type: {search_type}, limit: {limit})")
            
            # Validation
            if not query or not query.strip():
                raise ValueError("Query cannot be empty")
            
            if len(query) > 1000:
                raise ValueError("Query too long (max 1000 characters)")
            
            search_results = []
            
            # Try Neo4j knowledge graph search first
            if self.neo4j_handler and self.neo4j_handler._connected:
                try:
                    search_results = self._search_knowledge_graph(query, limit, search_type)
                    logger.info(f"Knowledge graph search returned {len(search_results)} results")
                except Exception as e:
                    logger.warning(f"Knowledge graph search failed, falling back to API: {str(e)}")
                    search_results = []
            
            # Fallback to GitHub API search if no results or Neo4j unavailable
            if not search_results and self.connection:
                try:
                    search_results = self._search_github_api(query, limit)
                    logger.info(f"GitHub API search returned {len(search_results)} results")
                except Exception as e:
                    logger.error(f"GitHub API search also failed: {str(e)}")
            
            # Calculate metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            metrics = SearchMetrics(
                execution_time_ms=execution_time,
                total_results_found=len(search_results),
                results_returned=len(search_results)
            )
            
            logger.info(f"Search completed in {execution_time:.2f}ms with {len(search_results)} results")
            
            # Return standardized response
            return ConnectorSearchResponse(
                status=SearchStatus.SUCCESS,
                query=query,
                results=search_results,
                total_count=len(search_results),
                connector_info=self._get_connector_info(),
                metrics=metrics
            )
            
        except ValueError as e:
            logger.error(f"Search validation error: {str(e)}")
            return ConnectorSearchResponse(
                status=SearchStatus.ERROR,
                query=query,
                results=[],
                total_count=0,
                connector_info=self._get_connector_info(),
                error=SearchError(
                    code="VALIDATION_ERROR",
                    message=str(e),
                    details={"query": query}
                )
            )
        except Exception as e:
            logger.error(f"Search execution error: {str(e)}")
            return ConnectorSearchResponse(
                status=SearchStatus.ERROR,
                query=query,
                results=[],
                total_count=0,
                connector_info=self._get_connector_info(),
                error=SearchError(
                    code="EXECUTION_ERROR",
                    message=f"Search failed: {str(e)}",
                    details={"query": query, "error_type": type(e).__name__}
                )
            )
    
    def _search_knowledge_graph(self, query: str, limit: int, search_type: str) -> List[SearchResultItem]:
        """
        Search using Neo4j knowledge graph
        
        Args:
            query: Search query
            limit: Result limit
            search_type: Type of search to perform
            
        Returns:
            List[SearchResultItem]: Search results
        """
        try:
            # Initialize knowledge graph search
            kg_search = GitHubKnowledgeGraphSearch(self.neo4j_handler)
            
            # Analyze query to determine optimal search strategy
            search_context = kg_search.analyze_query(query)
            search_context.limit = limit
            
            # Override search type if specified
            if search_type != "hybrid":
                search_type_map = {
                    "entity": SearchType.ENTITY_SEARCH,
                    "relationship": SearchType.RELATIONSHIP_SEARCH,
                    "traversal": SearchType.GRAPH_TRAVERSAL,
                    "semantic": SearchType.SEMANTIC_SEARCH
                }
                search_context.search_type = search_type_map.get(search_type, SearchType.HYBRID_SEARCH)
            
            # Execute search based on type
            if search_context.search_type == SearchType.ENTITY_SEARCH:
                results = kg_search.search_entities(search_context)
            elif search_context.search_type == SearchType.RELATIONSHIP_SEARCH:
                results = kg_search.search_relationships(search_context)
            elif search_context.search_type == SearchType.GRAPH_TRAVERSAL:
                results = kg_search.graph_traversal_search(search_context)
            else:
                results = kg_search.hybrid_search(search_context)
            
            logger.debug(f"Knowledge graph search strategy: {search_context.search_type.value}")
            logger.debug(f"Detected entities: {search_context.entity_types}")
            logger.debug(f"Detected relationships: {search_context.relationship_types}")
            
            return results
            
        except Exception as e:
            logger.error(f"Knowledge graph search error: {str(e)}")
            raise
    
    def _search_github_api(self, query: str, limit: int) -> List[SearchResultItem]:
        """
        Fallback search using GitHub API
        
        Args:
            query: Search query
            limit: Result limit
            
        Returns:
            List[SearchResultItem]: Search results
        """
        try:
            api_search = GitHubAPISearch(self.connection)
            
            # Simple repository search as fallback
            results = api_search.search_repositories_api(query, limit)
            
            logger.debug(f"GitHub API fallback search returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"GitHub API search error: {str(e)}")
            raise
            
        except ValueError as e:
            return self._create_error_response(query, "VALIDATION_ERROR", str(e), start_time)
        except Exception as e:
            return self._create_error_response(query, "SEARCH_ERROR", str(e), start_time)

    def _get_connector_info(self) -> Dict[str, str]:
        """Return connector information for responses"""
        return {
            "source_type": self.source_type,
            "name": self.connector_name,
            "version": self.version,
            "type": self.CONNECTOR_TYPE
        }

    def _create_error_response(self, query: str, error_code: str, error_message: str, start_time: datetime) -> ConnectorSearchResponse:
        """Create standardized error response"""
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ConnectorSearchResponse(
            status=SearchStatus.ERROR,
            query=query,
            connector_info=self._get_connector_info(),
            error=SearchError(
                error_code=error_code,
                error_message=error_message,
                error_type="validation_error" if error_code == "VALIDATION_ERROR" else "internal_error"
            ),
            metrics=SearchMetrics(
                execution_time_ms=execution_time,
                total_results_found=0,
                results_returned=0
            )
        )

    # Helper methods for data fetching
    def _fetch_repositories(self) -> Iterator[Dict[str, Any]]:
        """Fetch limited number of repositories for testing"""
        try:
            response = self.connection.make_request(
                'GET',
                f'/user/repos?per_page={self.data_limits["repositories"]}&sort=updated&direction=desc'
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to fetch repositories: {response.status_code}")
                return
            
            repos = response.json()
            count = 0
            
            for repo in repos:
                if count >= self.data_limits['repositories']:
                    break
                    
                yield {
                    'entity_type': 'repository',
                    'id': f"repository:{repo['id']}",
                    'github_id': repo['id'],
                    'name': repo['name'],
                    'full_name': repo['full_name'],
                    'description': repo.get('description'),
                    'private': repo['private'],
                    'fork': repo['fork'],
                    'owner': repo['owner'],
                    'html_url': repo['html_url'],
                    'clone_url': repo['clone_url'],
                    'language': repo.get('language'),
                    'stargazers_count': repo['stargazers_count'],
                    'watchers_count': repo['watchers_count'],
                    'forks_count': repo['forks_count'],
                    'open_issues_count': repo['open_issues_count'],
                    'default_branch': repo['default_branch'],
                    'topics': repo.get('topics', []),
                    'created_at': repo['created_at'],
                    'updated_at': repo['updated_at'],
                    'pushed_at': repo.get('pushed_at'),
                    'raw_data': repo
                }
                count += 1
                
        except Exception as e:
            logger.error(f"Error fetching repositories: {str(e)}")
            raise

    def _fetch_users(self) -> Iterator[Dict[str, Any]]:
        """Fetch users (contributors, collaborators, etc.)"""
        try:
            # This is a simplified implementation
            # In practice, you'd fetch users from various contexts
            response = self.connection.make_request('GET', '/user')
            
            if response.status_code == 200:
                user = response.json()
                yield {
                    'entity_type': 'user',
                    'id': f"user:{user['id']}",
                    'github_id': user['id'],
                    'login': user['login'],
                    'name': user.get('name'),
                    'email': user.get('email'),
                    'bio': user.get('bio'),
                    'company': user.get('company'),
                    'location': user.get('location'),
                    'blog': user.get('blog'),
                    'twitter_username': user.get('twitter_username'),
                    'public_repos': user['public_repos'],
                    'public_gists': user['public_gists'],
                    'followers': user['followers'],
                    'following': user['following'],
                    'created_at': user['created_at'],
                    'updated_at': user['updated_at'],
                    'avatar_url': user['avatar_url'],
                    'html_url': user['html_url'],
                    'type': user['type'],
                    'raw_data': user
                }
                
        except Exception as e:
            logger.error(f"Error fetching users: {str(e)}")
            raise

    def _fetch_organizations(self) -> Iterator[Dict[str, Any]]:
        """Fetch limited number of organizations for testing"""
        try:
            response = self.connection.make_request('GET', '/user/orgs')
            
            if response.status_code == 200:
                orgs = response.json()
                count = 0
                
                for org in orgs:
                    if count >= self.data_limits['organizations']:
                        break
                        
                    # Fetch detailed org info
                    org_response = self.connection.make_request('GET', f'/orgs/{org["login"]}')
                    if org_response.status_code == 200:
                        org_detail = org_response.json()
                        yield {
                            'entity_type': 'organization',
                            'id': f"organization:{org_detail['id']}",
                            'github_id': org_detail['id'],
                            'login': org_detail['login'],
                            'name': org_detail.get('name'),
                            'description': org_detail.get('description'),
                            'company': org_detail.get('company'),
                            'blog': org_detail.get('blog'),
                            'location': org_detail.get('location'),
                            'email': org_detail.get('email'),
                            'twitter_username': org_detail.get('twitter_username'),
                            'public_repos': org_detail['public_repos'],
                            'public_gists': org_detail['public_gists'],
                            'followers': org_detail['followers'],
                            'following': org_detail['following'],
                            'created_at': org_detail['created_at'],
                            'updated_at': org_detail['updated_at'],
                            'avatar_url': org_detail['avatar_url'],
                            'html_url': org_detail['html_url'],
                            'raw_data': org_detail
                        }
                        count += 1
                        
        except Exception as e:
            logger.error(f"Error fetching organizations: {str(e)}")
            raise

    def _fetch_issues_for_repository(self, repo_id: str) -> Iterator[Dict[str, Any]]:
        """Fetch limited number of issues for a specific repository"""
        try:
            # Get repository info first to construct the API endpoint
            repo_response = self.connection.make_request('GET', f'/repositories/{repo_id}')
            if repo_response.status_code != 200:
                logger.error(f"Failed to fetch repository {repo_id}")
                return
            
            repo_data = repo_response.json()
            repo_full_name = repo_data['full_name']
            
            # Fetch issues for this specific repository
            response = self.connection.make_request(
                'GET', 
                f'/repos/{repo_full_name}/issues?state=all&per_page={self.data_limits["issues"]}'
            )
            
            if response.status_code == 200:
                issues = response.json()
                count = 0
                
                for issue in issues:
                    if count >= self.data_limits['issues']:
                        break
                        
                    # Skip pull requests (they appear in issues endpoint)
                    if 'pull_request' in issue:
                        continue
                        
                    yield {
                        'entity_type': 'issue',
                        'id': f"issue:{issue['id']}",
                        'github_id': issue['id'],
                        'number': issue['number'],
                        'title': issue['title'],
                        'body': issue.get('body'),
                        'state': issue['state'],
                        'locked': issue['locked'],
                        'assignee': issue.get('assignee'),
                        'assignees': issue.get('assignees', []),
                        'milestone': issue.get('milestone'),
                        'labels': issue.get('labels', []),
                        'user': issue['user'],
                        'comments': issue['comments'],
                        'created_at': issue['created_at'],
                        'updated_at': issue['updated_at'],
                        'closed_at': issue.get('closed_at'),
                        'html_url': issue['html_url'],
                        'repository_url': issue['repository_url'],
                        'raw_data': issue
                    }
                    count += 1
                    
        except Exception as e:
            logger.error(f"Error fetching issues for repository {repo_id}: {str(e)}")
            raise

    def _fetch_repository_by_id(self, repo_id: str) -> Dict[str, Any]:
        """Fetch repository by ID"""
        response = self.connection.make_request('GET', f'/repositories/{repo_id}')
        if response.status_code == 200:
            repo = response.json()
            return {
                'entity_type': 'repository',
                'id': f"repository:{repo['id']}",
                'raw_data': repo,
                # ... other fields would be populated here
            }
        else:
            raise ValueError(f"Repository {repo_id} not found")

    def _fetch_user_by_id(self, user_id: str) -> Dict[str, Any]:
        """Fetch user by ID or login"""
        response = self.connection.make_request('GET', f'/users/{user_id}')
        if response.status_code == 200:
            user = response.json()
            return {
                'entity_type': 'user',
                'id': f"user:{user['id']}",
                'raw_data': user,
                # ... other fields would be populated here
            }
        else:
            raise ValueError(f"User {user_id} not found")

    def _fetch_organization_by_id(self, org_id: str) -> Dict[str, Any]:
        """Fetch organization by ID or login"""
        response = self.connection.make_request('GET', f'/orgs/{org_id}')
        if response.status_code == 200:
            org = response.json()
            return {
                'entity_type': 'organization',
                'id': f"organization:{org['id']}",
                'raw_data': org,
                # ... other fields would be populated here
            }
        else:
            raise ValueError(f"Organization {org_id} not found")