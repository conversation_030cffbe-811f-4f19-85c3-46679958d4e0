"""
Neo4j Knowledge Graph Handler for GitHub Connector

This module handles Neo4j database operations for storing GitHub data
as a knowledge graph with nodes and relationships.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from neo4j import GraphDatabase, Driver, Session
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class GitHubNeo4jHandler:
    """
    Handles Neo4j operations for GitHub connector knowledge graph storage.
    """
    
    def __init__(self):
        """Initialize Neo4j connection using environment variables."""
        self.uri = os.getenv('NEO4J_URI')
        self.user = os.getenv('NEO4J_USER')
        self.password = os.getenv('NEO4J_PASSWORD')
        self.database = os.getenv('NEO4j_DATABASE', 'neo4j')
        
        self.driver: Optional[Driver] = None
        self._connected = False
        
        if not all([self.uri, self.user, self.password]):
            raise ValueError("Neo4j credentials not found in environment variables")
    
    def connect(self) -> bool:
        """
        Establish connection to Neo4j database.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info("Connecting to Neo4j database...")
            self.driver = GraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password)
            )
            
            # Test connection
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    self._connected = True
                    logger.info("Successfully connected to Neo4j database")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            self._connected = False
            return False
    
    def close(self):
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            self._connected = False
            logger.info("Neo4j connection closed")
    
    def create_repository_node(self, repo_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubRepository node in Neo4j.
        
        Args:
            repo_data: Repository data dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (r:GitHubRepository {github_id: $github_id})
                SET r.name = $name,
                    r.full_name = $full_name,
                    r.description = $description,
                    r.private = $private,
                    r.fork = $fork,
                    r.html_url = $html_url,
                    r.clone_url = $clone_url,
                    r.language = $language,
                    r.stargazers_count = $stargazers_count,
                    r.watchers_count = $watchers_count,
                    r.forks_count = $forks_count,
                    r.open_issues_count = $open_issues_count,
                    r.default_branch = $default_branch,
                    r.topics = $topics,
                    r.created_at = $created_at,
                    r.updated_at = $updated_at,
                    r.pushed_at = $pushed_at,
                    r.last_synced = $last_synced
                RETURN r.github_id as id
                """
                
                result = session.run(query, {
                    'github_id': repo_data.get('github_id'),
                    'name': repo_data.get('name'),
                    'full_name': repo_data.get('full_name'),
                    'description': repo_data.get('description'),
                    'private': repo_data.get('private', False),
                    'fork': repo_data.get('fork', False),
                    'html_url': repo_data.get('html_url'),
                    'clone_url': repo_data.get('clone_url'),
                    'language': repo_data.get('language'),
                    'stargazers_count': repo_data.get('stargazers_count', 0),
                    'watchers_count': repo_data.get('watchers_count', 0),
                    'forks_count': repo_data.get('forks_count', 0),
                    'open_issues_count': repo_data.get('open_issues_count', 0),
                    'default_branch': repo_data.get('default_branch'),
                    'topics': repo_data.get('topics', []),
                    'created_at': repo_data.get('created_at'),
                    'updated_at': repo_data.get('updated_at'),
                    'pushed_at': repo_data.get('pushed_at'),
                    'last_synced': datetime.now().isoformat()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"Created/updated repository node: {record['id']}")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to create repository node: {str(e)}")
            return False
    
    def create_user_node(self, user_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubUser node in Neo4j.
        
        Args:
            user_data: User data dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (u:GitHubUser {github_id: $github_id})
                SET u.login = $login,
                    u.name = $name,
                    u.email = $email,
                    u.bio = $bio,
                    u.company = $company,
                    u.location = $location,
                    u.blog = $blog,
                    u.twitter_username = $twitter_username,
                    u.public_repos = $public_repos,
                    u.public_gists = $public_gists,
                    u.followers = $followers,
                    u.following = $following,
                    u.created_at = $created_at,
                    u.updated_at = $updated_at,
                    u.avatar_url = $avatar_url,
                    u.html_url = $html_url,
                    u.type = $type,
                    u.last_synced = $last_synced
                RETURN u.github_id as id
                """
                
                result = session.run(query, {
                    'github_id': user_data.get('github_id'),
                    'login': user_data.get('login'),
                    'name': user_data.get('name'),
                    'email': user_data.get('email'),
                    'bio': user_data.get('bio'),
                    'company': user_data.get('company'),
                    'location': user_data.get('location'),
                    'blog': user_data.get('blog'),
                    'twitter_username': user_data.get('twitter_username'),
                    'public_repos': user_data.get('public_repos', 0),
                    'public_gists': user_data.get('public_gists', 0),
                    'followers': user_data.get('followers', 0),
                    'following': user_data.get('following', 0),
                    'created_at': user_data.get('created_at'),
                    'updated_at': user_data.get('updated_at'),
                    'avatar_url': user_data.get('avatar_url'),
                    'html_url': user_data.get('html_url'),
                    'type': user_data.get('type'),
                    'last_synced': datetime.now().isoformat()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"Created/updated user node: {record['id']}")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to create user node: {str(e)}")
            return False
    
    def create_organization_node(self, org_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubOrganization node in Neo4j.
        
        Args:
            org_data: Organization data dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (o:GitHubOrganization {github_id: $github_id})
                SET o.login = $login,
                    o.name = $name,
                    o.description = $description,
                    o.company = $company,
                    o.blog = $blog,
                    o.location = $location,
                    o.email = $email,
                    o.twitter_username = $twitter_username,
                    o.public_repos = $public_repos,
                    o.public_gists = $public_gists,
                    o.followers = $followers,
                    o.following = $following,
                    o.created_at = $created_at,
                    o.updated_at = $updated_at,
                    o.avatar_url = $avatar_url,
                    o.html_url = $html_url,
                    o.last_synced = $last_synced
                RETURN o.github_id as id
                """
                
                result = session.run(query, {
                    'github_id': org_data.get('github_id'),
                    'login': org_data.get('login'),
                    'name': org_data.get('name'),
                    'description': org_data.get('description'),
                    'company': org_data.get('company'),
                    'blog': org_data.get('blog'),
                    'location': org_data.get('location'),
                    'email': org_data.get('email'),
                    'twitter_username': org_data.get('twitter_username'),
                    'public_repos': org_data.get('public_repos', 0),
                    'public_gists': org_data.get('public_gists', 0),
                    'followers': org_data.get('followers', 0),
                    'following': org_data.get('following', 0),
                    'created_at': org_data.get('created_at'),
                    'updated_at': org_data.get('updated_at'),
                    'avatar_url': org_data.get('avatar_url'),
                    'html_url': org_data.get('html_url'),
                    'last_synced': datetime.now().isoformat()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"Created/updated organization node: {record['id']}")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to create organization node: {str(e)}")
            return False
    
    def create_issue_node(self, issue_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubIssue node in Neo4j.
        
        Args:
            issue_data: Issue data dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (i:GitHubIssue {github_id: $github_id})
                SET i.number = $number,
                    i.title = $title,
                    i.body = $body,
                    i.state = $state,
                    i.locked = $locked,
                    i.comments = $comments,
                    i.created_at = $created_at,
                    i.updated_at = $updated_at,
                    i.closed_at = $closed_at,
                    i.html_url = $html_url,
                    i.repository_url = $repository_url,
                    i.last_synced = $last_synced
                RETURN i.github_id as id
                """
                
                result = session.run(query, {
                    'github_id': issue_data.get('github_id'),
                    'number': issue_data.get('number'),
                    'title': issue_data.get('title'),
                    'body': issue_data.get('body'),
                    'state': issue_data.get('state'),
                    'locked': issue_data.get('locked', False),
                    'comments': issue_data.get('comments', 0),
                    'created_at': issue_data.get('created_at'),
                    'updated_at': issue_data.get('updated_at'),
                    'closed_at': issue_data.get('closed_at'),
                    'html_url': issue_data.get('html_url'),
                    'repository_url': issue_data.get('repository_url'),
                    'last_synced': datetime.now().isoformat()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"Created/updated issue node: {record['id']}")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to create issue node: {str(e)}")
            return False

    def create_pull_request_node(self, pr_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubPullRequest node in Neo4j.

        Args:
            pr_data: Pull request data dictionary

        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False

        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (pr:GitHubPullRequest {github_id: $github_id})
                SET pr.number = $number,
                    pr.title = $title,
                    pr.body = $body,
                    pr.state = $state,
                    pr.locked = $locked,
                    pr.merged = $merged,
                    pr.mergeable = $mergeable,
                    pr.draft = $draft,
                    pr.head_ref = $head_ref,
                    pr.base_ref = $base_ref,
                    pr.commits = $commits,
                    pr.additions = $additions,
                    pr.deletions = $deletions,
                    pr.changed_files = $changed_files,
                    pr.created_at = $created_at,
                    pr.updated_at = $updated_at,
                    pr.closed_at = $closed_at,
                    pr.merged_at = $merged_at,
                    pr.html_url = $html_url,
                    pr.last_synced = $last_synced
                RETURN pr.github_id as id
                """

                result = session.run(query, {
                    'github_id': pr_data.get('github_id'),
                    'number': pr_data.get('number'),
                    'title': pr_data.get('title'),
                    'body': pr_data.get('body'),
                    'state': pr_data.get('state'),
                    'locked': pr_data.get('locked', False),
                    'merged': pr_data.get('merged', False),
                    'mergeable': pr_data.get('mergeable'),
                    'draft': pr_data.get('draft', False),
                    'head_ref': pr_data.get('head', {}).get('ref') if pr_data.get('head') else None,
                    'base_ref': pr_data.get('base', {}).get('ref') if pr_data.get('base') else None,
                    'commits': pr_data.get('commits', 0),
                    'additions': pr_data.get('additions', 0),
                    'deletions': pr_data.get('deletions', 0),
                    'changed_files': pr_data.get('changed_files', 0),
                    'created_at': pr_data.get('created_at'),
                    'updated_at': pr_data.get('updated_at'),
                    'closed_at': pr_data.get('closed_at'),
                    'merged_at': pr_data.get('merged_at'),
                    'html_url': pr_data.get('html_url'),
                    'last_synced': datetime.now().isoformat()
                })

                record = result.single()
                if record:
                    logger.debug(f"Created/updated pull request node: {record['id']}")
                    return True

        except Exception as e:
            logger.error(f"Failed to create pull request node: {str(e)}")
            return False

    def create_commit_node(self, commit_data: Dict[str, Any]) -> bool:
        """
        Create a GitHubCommit node in Neo4j.

        Args:
            commit_data: Commit data dictionary

        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False

        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MERGE (c:GitHubCommit {sha: $sha})
                SET c.message = $message,
                    c.author_name = $author_name,
                    c.author_email = $author_email,
                    c.committer_name = $committer_name,
                    c.committer_email = $committer_email,
                    c.authored_date = $authored_date,
                    c.committed_date = $committed_date,
                    c.html_url = $html_url,
                    c.last_synced = $last_synced
                RETURN c.sha as id
                """

                commit_info = commit_data.get('commit', {})
                author_info = commit_info.get('author', {})
                committer_info = commit_info.get('committer', {})

                result = session.run(query, {
                    'sha': commit_data.get('sha'),
                    'message': commit_info.get('message'),
                    'author_name': author_info.get('name'),
                    'author_email': author_info.get('email'),
                    'committer_name': committer_info.get('name'),
                    'committer_email': committer_info.get('email'),
                    'authored_date': author_info.get('date'),
                    'committed_date': committer_info.get('date'),
                    'html_url': commit_data.get('html_url'),
                    'last_synced': datetime.now().isoformat()
                })

                record = result.single()
                if record:
                    logger.debug(f"Created/updated commit node: {record['id']}")
                    return True

        except Exception as e:
            logger.error(f"Failed to create commit node: {str(e)}")
            return False

    def create_relationship(self, from_node_type: str, from_node_id: str,
                          to_node_type: str, to_node_id: str, 
                          relationship_type: str, properties: Dict[str, Any] = None) -> bool:
        """
        Create a relationship between two nodes in Neo4j.
        
        Args:
            from_node_type: Source node type (e.g., 'GitHubUser')
            from_node_id: Source node github_id
            to_node_type: Target node type (e.g., 'GitHubRepository')
            to_node_id: Target node github_id
            relationship_type: Type of relationship (e.g., 'OWNS_REPOSITORY')
            properties: Optional relationship properties
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                # Build the query dynamically
                query = f"""
                MATCH (from:{from_node_type} {{github_id: $from_id}})
                MATCH (to:{to_node_type} {{github_id: $to_id}})
                MERGE (from)-[r:{relationship_type}]->(to)
                """
                
                params = {
                    'from_id': from_node_id,
                    'to_id': to_node_id
                }
                
                # Add properties if provided
                if properties:
                    prop_sets = []
                    for key, value in properties.items():
                        prop_sets.append(f"r.{key} = ${key}")
                        params[key] = value
                    
                    if prop_sets:
                        query += f"SET {', '.join(prop_sets)}"
                
                query += " RETURN r"
                
                result = session.run(query, params)
                record = result.single()
                
                if record:
                    logger.debug(f"Created relationship: {from_node_type}({from_node_id}) -[{relationship_type}]-> {to_node_type}({to_node_id})")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to create relationship: {str(e)}")
            return False
    
    def store_github_entity(self, entity_data: Dict[str, Any]) -> bool:
        """
        Store a GitHub entity and its relationships in Neo4j.
        
        Args:
            entity_data: Entity data with type and relationships
            
        Returns:
            bool: True if successful, False otherwise
        """
        entity_type = entity_data.get('entity_type')
        
        try:
            # Create the main entity node
            if entity_type == 'repository':
                success = self.create_repository_node(entity_data)
            elif entity_type == 'user':
                success = self.create_user_node(entity_data)
            elif entity_type == 'organization':
                success = self.create_organization_node(entity_data)
            elif entity_type == 'issue':
                success = self.create_issue_node(entity_data)
            elif entity_type == 'pull_request':
                success = self.create_pull_request_node(entity_data)
            elif entity_type == 'commit':
                success = self.create_commit_node(entity_data)
            else:
                logger.warning(f"Unsupported entity type: {entity_type}")
                return False
            
            if not success:
                return False
            
            # Create relationships if specified
            relationships = entity_data.get('relationships', [])
            for rel in relationships:
                self.create_relationship(
                    rel['from_node_type'],
                    rel['from_node_id'],
                    rel['to_node_type'],
                    rel['to_node_id'],
                    rel['relationship_type'],
                    rel.get('properties', {})
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to store GitHub entity: {str(e)}")
            return False
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def get_session(self):
        """
        Get a Neo4j session for database operations.

        Returns:
            Session: Neo4j session object
        """
        if not self._connected or not self.driver:
            raise ConnectionError("Neo4j not connected. Call connect() first.")

        return self.driver.session(database=self.database)

    def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Execute a Cypher query and return results.

        Args:
            query: Cypher query string
            parameters: Query parameters

        Returns:
            List[Dict]: Query results
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return []

        try:
            with self.get_session() as session:
                result = session.run(query, parameters or {})
                return [dict(record) for record in result]
        except Exception as e:
            logger.error(f"Failed to execute query: {str(e)}")
            return []

    def clear_all_data(self) -> bool:
        """
        Clear all GitHub data from Neo4j (for testing purposes).

        Returns:
            bool: True if successful
        """
        if not self._connected:
            logger.error("Neo4j not connected")
            return False

        try:
            with self.get_session() as session:
                # Delete all GitHub nodes and relationships
                session.run("MATCH (n) WHERE any(label IN labels(n) WHERE label STARTS WITH 'GitHub') DETACH DELETE n")
                logger.info("Cleared all GitHub data from Neo4j")
                return True
        except Exception as e:
            logger.error(f"Failed to clear data: {str(e)}")
            return False

    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about stored GitHub data.

        Returns:
            Dict: Statistics about nodes and relationships
        """
        stats = {}

        try:
            with self.get_session() as session:
                # Count nodes by type
                for entity_type in ['GitHubRepository', 'GitHubUser', 'GitHubOrganization', 'GitHubIssue']:
                    result = session.run(f"MATCH (n:{entity_type}) RETURN count(n) as count")
                    record = result.single()
                    stats[f"{entity_type}_count"] = record['count'] if record else 0

                # Count total relationships
                result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
                record = result.single()
                stats['total_relationships'] = record['count'] if record else 0

        except Exception as e:
            logger.error(f"Failed to get statistics: {str(e)}")

        return stats

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()