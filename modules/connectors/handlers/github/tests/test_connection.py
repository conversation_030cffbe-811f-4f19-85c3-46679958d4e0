"""
Tests for GitHub Connection Handler
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import aiohttp
from datetime import datetime, timedelta

from ..connection import GitHubConnectionHandler
from ..schema import GitHubConfig


class TestGitHubConnectionHandler:
    """Test cases for GitHub connection handler"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return GitHubConfig(
            token="test_token",
            base_url="https://api.github.com",
            timeout=30,
            max_retries=3,
            rate_limit_buffer=100
        )

    @pytest.fixture
    def handler(self, config):
        """Test handler instance"""
        return GitHubConnectionHandler(config)

    def test_init(self, config):
        """Test handler initialization"""
        handler = GitHubConnectionHandler(config)
        assert handler.config == config
        assert handler.session is None
        assert handler.rate_limit_remaining == 5000
        assert handler.rate_limit_reset is None

    @pytest.mark.asyncio
    async def test_connect_success(self, handler):
        """Test successful connection"""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__ = AsyncMock()
            mock_session.return_value.__aexit__ = AsyncMock()
            
            await handler.connect()
            assert handler.session is not None

    @pytest.mark.asyncio
    async def test_disconnect(self, handler):
        """Test disconnection"""
        # Mock session
        handler.session = Mock()
        handler.session.close = AsyncMock()
        
        await handler.disconnect()
        handler.session.close.assert_called_once()
        assert handler.session is None

    @pytest.mark.asyncio
    async def test_health_check_success(self, handler):
        """Test successful health check"""
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"rate": {"remaining": 4500}})
        
        handler.session = Mock()
        handler.session.get = AsyncMock(return_value=mock_response)
        
        result = await handler.health_check()
        assert result is True
        assert handler.rate_limit_remaining == 4500

    @pytest.mark.asyncio
    async def test_health_check_failure(self, handler):
        """Test failed health check"""
        mock_response = Mock()
        mock_response.status = 401
        
        handler.session = Mock()
        handler.session.get = AsyncMock(return_value=mock_response)
        
        result = await handler.health_check()
        assert result is False

    @pytest.mark.asyncio
    async def test_make_request_success(self, handler):
        """Test successful API request"""
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"test": "data"})
        mock_response.headers = {
            "X-RateLimit-Remaining": "4500",
            "X-RateLimit-Reset": "**********"
        }
        
        handler.session = Mock()
        handler.session.get = AsyncMock(return_value=mock_response)
        
        result = await handler.make_request("GET", "/test")
        assert result == {"test": "data"}
        assert handler.rate_limit_remaining == 4500

    @pytest.mark.asyncio
    async def test_make_request_rate_limit(self, handler):
        """Test rate limit handling"""
        handler.rate_limit_remaining = 50  # Below buffer
        handler.rate_limit_reset = datetime.now() + timedelta(seconds=10)
        
        with patch('asyncio.sleep') as mock_sleep:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={"test": "data"})
            mock_response.headers = {
                "X-RateLimit-Remaining": "4500",
                "X-RateLimit-Reset": "**********"
            }
            
            handler.session = Mock()
            handler.session.get = AsyncMock(return_value=mock_response)
            
            result = await handler.make_request("GET", "/test")
            mock_sleep.assert_called_once()
            assert result == {"test": "data"}

    @pytest.mark.asyncio
    async def test_make_request_retry(self, handler):
        """Test request retry logic"""
        # First call fails, second succeeds
        mock_response_fail = Mock()
        mock_response_fail.status = 500
        
        mock_response_success = Mock()
        mock_response_success.status = 200
        mock_response_success.json = AsyncMock(return_value={"test": "data"})
        mock_response_success.headers = {
            "X-RateLimit-Remaining": "4500",
            "X-RateLimit-Reset": "**********"
        }
        
        handler.session = Mock()
        handler.session.get = AsyncMock(side_effect=[mock_response_fail, mock_response_success])
        
        with patch('asyncio.sleep'):
            result = await handler.make_request("GET", "/test")
            assert result == {"test": "data"}
            assert handler.session.get.call_count == 2

    @pytest.mark.asyncio
    async def test_make_request_max_retries(self, handler):
        """Test max retries exceeded"""
        mock_response = Mock()
        mock_response.status = 500
        
        handler.session = Mock()
        handler.session.get = AsyncMock(return_value=mock_response)
        
        with patch('asyncio.sleep'):
            with pytest.raises(Exception):
                await handler.make_request("GET", "/test")

    def test_update_rate_limit(self, handler):
        """Test rate limit update"""
        headers = {
            "X-RateLimit-Remaining": "4000",
            "X-RateLimit-Reset": "**********"
        }
        
        handler._update_rate_limit(headers)
        assert handler.rate_limit_remaining == 4000
        assert handler.rate_limit_reset is not None

    def test_should_wait_for_rate_limit(self, handler):
        """Test rate limit wait logic"""
        # Above buffer - should not wait
        handler.rate_limit_remaining = 200
        assert not handler._should_wait_for_rate_limit()
        
        # Below buffer - should wait
        handler.rate_limit_remaining = 50
        handler.rate_limit_reset = datetime.now() + timedelta(seconds=10)
        assert handler._should_wait_for_rate_limit()
        
        # Below buffer but reset time passed - should not wait
        handler.rate_limit_reset = datetime.now() - timedelta(seconds=10)
        assert not handler._should_wait_for_rate_limit()

    @pytest.mark.asyncio
    async def test_paginated_request(self, handler):
        """Test paginated request handling"""
        # Mock responses for pagination
        page1_response = Mock()
        page1_response.status = 200
        page1_response.json = AsyncMock(return_value=[{"id": 1}, {"id": 2}])
        page1_response.headers = {
            "Link": '<https://api.github.com/test?page=2>; rel="next"',
            "X-RateLimit-Remaining": "4500"
        }
        
        page2_response = Mock()
        page2_response.status = 200
        page2_response.json = AsyncMock(return_value=[{"id": 3}])
        page2_response.headers = {"X-RateLimit-Remaining": "4499"}
        
        handler.session = Mock()
        handler.session.get = AsyncMock(side_effect=[page1_response, page2_response])
        
        results = []
        async for page in handler.paginated_request("GET", "/test"):
            results.extend(page)
        
        assert len(results) == 3
        assert results[0]["id"] == 1
        assert results[2]["id"] == 3

    def test_parse_link_header(self, handler):
        """Test link header parsing"""
        link_header = '<https://api.github.com/test?page=2>; rel="next", <https://api.github.com/test?page=5>; rel="last"'
        
        links = handler._parse_link_header(link_header)
        assert "next" in links
        assert "last" in links
        assert links["next"] == "https://api.github.com/test?page=2"
        assert links["last"] == "https://api.github.com/test?page=5"

    def test_parse_link_header_empty(self, handler):
        """Test empty link header parsing"""
        links = handler._parse_link_header("")
        assert links == {}
        
        links = handler._parse_link_header(None)
        assert links == {}