"""
Pytest configuration and fixtures for GitHub connector tests
"""

import pytest
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from ..schema import GitHubConfig
from ..connection import GitHubConnectionHandler
from ..service import GitHubConnectorService


@pytest.fixture
def github_config():
    """GitHub configuration fixture"""
    return {
        "token": "ghp_test_token_1234567890abcdef",
        "base_url": "https://api.github.com",
        "timeout": 30,
        "max_retries": 3,
        "rate_limit_buffer": 100,
        "postgresql": {
            "host": "localhost",
            "port": 5432,
            "database": "test_github_db",
            "username": "github_user",
            "password": "github_pass"
        },
        "pinecone": {
            "api_key": "test_pinecone_key_abcd1234",
            "environment": "test-environment",
            "index_name": "github-connector-index"
        }
    }


@pytest.fixture
def github_config_schema(github_config):
    """GitHub configuration schema fixture"""
    return GitHubConfig(**github_config)


@pytest.fixture
def mock_connection_handler():
    """Mock GitHub connection handler"""
    handler = Mock(spec=GitHubConnectionHandler)
    handler.connect = AsyncMock(return_value=True)
    handler.disconnect = AsyncMock()
    handler.health_check = AsyncMock(return_value=True)
    handler.make_request = AsyncMock()
    handler.paginated_request = AsyncMock()
    handler.rate_limit_remaining = 5000
    handler.rate_limit_reset = None
    return handler


@pytest.fixture
def github_service(github_config, mock_connection_handler):
    """GitHub service fixture with mocked connection"""
    service = GitHubConnectorService(github_config)
    service.connection_handler = mock_connection_handler
    return service


@pytest.fixture
def sample_repository():
    """Sample repository data"""
    return {
        "id": 123456789,
        "node_id": "MDEwOlJlcG9zaXRvcnkxMjM0NTY3ODk=",
        "name": "test-repository",
        "full_name": "octocat/test-repository",
        "description": "A comprehensive test repository for GitHub connector",
        "private": False,
        "html_url": "https://github.com/octocat/test-repository",
        "clone_url": "https://github.com/octocat/test-repository.git",
        "ssh_url": "**************:octocat/test-repository.git",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-12-31T23:59:59Z",
        "pushed_at": "2023-12-31T20:00:00Z",
        "language": "Python",
        "size": 2048,
        "stargazers_count": 150,
        "watchers_count": 150,
        "forks_count": 25,
        "open_issues_count": 8,
        "default_branch": "main",
        "archived": False,
        "disabled": False,
        "visibility": "public",
        "topics": ["python", "api", "connector", "github"],
        "license": {
            "key": "mit",
            "name": "MIT License",
            "spdx_id": "MIT"
        }
    }


@pytest.fixture
def sample_user():
    """Sample user data"""
    return {
        "id": 583231,
        "node_id": "MDQ6VXNlcjU4MzIzMQ==",
        "login": "octocat",
        "avatar_url": "https://github.com/images/error/octocat_happy.gif",
        "gravatar_id": "",
        "url": "https://api.github.com/users/octocat",
        "html_url": "https://github.com/octocat",
        "type": "User",
        "site_admin": False,
        "name": "The Octocat",
        "company": "GitHub",
        "blog": "https://github.com/blog",
        "location": "San Francisco",
        "email": "<EMAIL>",
        "hireable": None,
        "bio": "There once was...",
        "twitter_username": "github",
        "public_repos": 8,
        "public_gists": 8,
        "followers": 4000,
        "following": 9,
        "created_at": "2011-01-25T18:44:36Z",
        "updated_at": "2023-12-31T12:00:00Z"
    }


@pytest.fixture
def sample_issue():
    """Sample issue data"""
    return {
        "id": 1,
        "node_id": "MDU6SXNzdWUx",
        "url": "https://api.github.com/repos/octocat/Hello-World/issues/1347",
        "repository_url": "https://api.github.com/repos/octocat/Hello-World",
        "labels_url": "https://api.github.com/repos/octocat/Hello-World/issues/1347/labels{/name}",
        "comments_url": "https://api.github.com/repos/octocat/Hello-World/issues/1347/comments",
        "events_url": "https://api.github.com/repos/octocat/Hello-World/issues/1347/events",
        "html_url": "https://github.com/octocat/Hello-World/issues/1347",
        "number": 1347,
        "state": "open",
        "title": "Found a critical bug in authentication",
        "body": "I'm having a problem with user authentication. When users try to log in with special characters in their password, the system throws a 500 error.",
        "user": {
            "login": "octocat",
            "id": 1,
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/octocat"
        },
        "labels": [
            {
                "id": 208045946,
                "node_id": "MDU6TGFiZWwyMDgwNDU5NDY=",
                "url": "https://api.github.com/repos/octocat/Hello-World/labels/bug",
                "name": "bug",
                "description": "Something isn't working",
                "color": "d73a4a",
                "default": True
            },
            {
                "id": 208045947,
                "node_id": "MDU6TGFiZWwyMDgwNDU5NDc=",
                "url": "https://api.github.com/repos/octocat/Hello-World/labels/critical",
                "name": "critical",
                "description": "Critical priority issue",
                "color": "ff0000",
                "default": False
            }
        ],
        "assignee": {
            "login": "octocat",
            "id": 1,
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/octocat"
        },
        "assignees": [
            {
                "login": "octocat",
                "id": 1,
                "avatar_url": "https://github.com/images/error/octocat_happy.gif",
                "html_url": "https://github.com/octocat"
            }
        ],
        "milestone": {
            "id": 1002604,
            "number": 1,
            "state": "open",
            "title": "v1.0",
            "description": "Tracking milestone for version 1.0",
            "creator": {
                "login": "octocat",
                "id": 1,
                "avatar_url": "https://github.com/images/error/octocat_happy.gif",
                "html_url": "https://github.com/octocat"
            },
            "open_issues": 4,
            "closed_issues": 8,
            "created_at": "2011-04-10T20:09:31Z",
            "updated_at": "2014-03-03T18:58:10Z",
            "closed_at": None,
            "due_on": "2012-10-09T23:39:01Z"
        },
        "locked": False,
        "active_lock_reason": None,
        "comments": 0,
        "pull_request": None,
        "closed_at": None,
        "created_at": "2011-04-22T13:33:48Z",
        "updated_at": "2011-04-22T13:33:48Z",
        "author_association": "COLLABORATOR"
    }


@pytest.fixture
def sample_pull_request():
    """Sample pull request data"""
    return {
        "id": 1,
        "node_id": "MDExOlB1bGxSZXF1ZXN0MQ==",
        "url": "https://api.github.com/repos/octocat/Hello-World/pulls/1347",
        "html_url": "https://github.com/octocat/Hello-World/pull/1347",
        "diff_url": "https://github.com/octocat/Hello-World/pull/1347.diff",
        "patch_url": "https://github.com/octocat/Hello-World/pull/1347.patch",
        "issue_url": "https://api.github.com/repos/octocat/Hello-World/issues/1347",
        "commits_url": "https://api.github.com/repos/octocat/Hello-World/pulls/1347/commits",
        "review_comments_url": "https://api.github.com/repos/octocat/Hello-World/pulls/1347/comments",
        "review_comment_url": "https://api.github.com/repos/octocat/Hello-World/pulls/comments{/number}",
        "comments_url": "https://api.github.com/repos/octocat/Hello-World/issues/1347/comments",
        "statuses_url": "https://api.github.com/repos/octocat/Hello-World/statuses/6dcb09b5b57875f334f61aebed695e2e4193db5e",
        "number": 1347,
        "state": "open",
        "locked": False,
        "title": "Implement user authentication improvements",
        "body": "This PR implements several improvements to user authentication:\n\n- Fix special character handling in passwords\n- Add rate limiting for login attempts\n- Improve error messages\n- Add comprehensive tests",
        "created_at": "2011-01-26T19:01:12Z",
        "updated_at": "2011-01-26T19:14:43Z",
        "closed_at": None,
        "merged_at": None,
        "merge_commit_sha": None,
        "head": {
            "label": "octocat:new-topic",
            "ref": "new-topic",
            "sha": "6dcb09b5b57875f334f61aebed695e2e4193db5e"
        },
        "base": {
            "label": "octocat:master",
            "ref": "master",
            "sha": "6dcb09b5b57875f334f61aebed695e2e4193db5e"
        },
        "merged": False,
        "mergeable": True,
        "rebaseable": True,
        "mergeable_state": "clean",
        "merged_by": None,
        "comments": 10,
        "review_comments": 0,
        "maintainer_can_modify": True,
        "commits": 3,
        "additions": 100,
        "deletions": 3,
        "changed_files": 5,
        "draft": False
    }


@pytest.fixture
def sample_commit():
    """Sample commit data"""
    return {
        "sha": "6dcb09b5b57875f334f61aebed695e2e4193db5e",
        "node_id": "MDY6Q29tbWl0NmRjYjA5YjViNTc4NzVmMzM0ZjYxYWViZWQ2OTVlMmU0MTkzZGI1ZQ==",
        "url": "https://api.github.com/repos/octocat/Hello-World/commits/6dcb09b5b57875f334f61aebed695e2e4193db5e",
        "html_url": "https://github.com/octocat/Hello-World/commit/6dcb09b5b57875f334f61aebed695e2e4193db5e",
        "comments_url": "https://api.github.com/repos/octocat/Hello-World/commits/6dcb09b5b57875f334f61aebed695e2e4193db5e/comments",
        "commit": {
            "url": "https://api.github.com/repos/octocat/Hello-World/git/commits/6dcb09b5b57875f334f61aebed695e2e4193db5e",
            "author": {
                "name": "Monalisa Octocat",
                "email": "<EMAIL>",
                "date": "2011-04-14T16:00:49Z"
            },
            "committer": {
                "name": "Monalisa Octocat",
                "email": "<EMAIL>",
                "date": "2011-04-14T16:00:49Z"
            },
            "message": "Fix authentication bug with special characters\n\nThis commit resolves the issue where passwords containing special\ncharacters would cause a 500 error during login attempts.",
            "tree": {
                "url": "https://api.github.com/repos/octocat/Hello-World/git/trees/827efc6d56897b048c772eb4087f854f46256132",
                "sha": "827efc6d56897b048c772eb4087f854f46256132"
            },
            "comment_count": 0,
            "verification": {
                "verified": False,
                "reason": "unsigned",
                "signature": None,
                "payload": None
            }
        },
        "author": {
            "login": "octocat",
            "id": 1,
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/octocat"
        },
        "committer": {
            "login": "octocat",
            "id": 1,
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/octocat"
        },
        "parents": [
            {
                "url": "https://api.github.com/repos/octocat/Hello-World/commits/553c2077f0edc3d5dc5d17262f6aa498e69d6f8e",
                "sha": "553c2077f0edc3d5dc5d17262f6aa498e69d6f8e"
            }
        ],
        "stats": {
            "additions": 104,
            "deletions": 4,
            "total": 108
        },
        "files": [
            {
                "sha": "bbcd538c8e72b8c175046e27cc8f907076331401",
                "filename": "file1.txt",
                "status": "added",
                "additions": 103,
                "deletions": 0,
                "changes": 103,
                "blob_url": "https://github.com/octocat/Hello-World/blob/6dcb09b5b57875f334f61aebed695e2e4193db5e/file1.txt",
                "raw_url": "https://github.com/octocat/Hello-World/raw/6dcb09b5b57875f334f61aebed695e2e4193db5e/file1.txt",
                "contents_url": "https://api.github.com/repos/octocat/Hello-World/contents/file1.txt?ref=6dcb09b5b57875f334f61aebed695e2e4193db5e",
                "patch": "@@ -0,0 +1,103 @@\n+# Authentication Module\n+\n+This module handles user authentication..."
            }
        ]
    }


@pytest.fixture
def sample_organization():
    """Sample organization data"""
    return {
        "id": 1,
        "node_id": "MDEyOk9yZ2FuaXphdGlvbjE=",
        "url": "https://api.github.com/orgs/github",
        "repos_url": "https://api.github.com/orgs/github/repos",
        "events_url": "https://api.github.com/orgs/github/events",
        "hooks_url": "https://api.github.com/orgs/github/hooks",
        "issues_url": "https://api.github.com/orgs/github/issues",
        "members_url": "https://api.github.com/orgs/github/members{/member}",
        "public_members_url": "https://api.github.com/orgs/github/public_members{/member}",
        "avatar_url": "https://github.com/images/error/octocat_happy.gif",
        "description": "A great organization",
        "name": "GitHub",
        "company": None,
        "blog": "https://github.com/blog",
        "location": "San Francisco",
        "email": "<EMAIL>",
        "twitter_username": "github",
        "is_verified": True,
        "has_organization_projects": True,
        "has_repository_projects": True,
        "public_repos": 2,
        "public_gists": 1,
        "followers": 20,
        "following": 0,
        "html_url": "https://github.com/github",
        "created_at": "2008-01-14T04:33:35Z",
        "updated_at": "2014-03-03T18:58:10Z",
        "type": "Organization",
        "total_private_repos": 100,
        "owned_private_repos": 100,
        "private_gists": 81,
        "disk_usage": 10000,
        "collaborators": 8,
        "billing_email": "<EMAIL>",
        "plan": {
            "name": "Medium",
            "space": 400,
            "private_repos": 20,
            "filled_seats": 4,
            "seats": 5
        },
        "default_repository_permission": "read",
        "members_can_create_repositories": True,
        "two_factor_requirement_enabled": False,
        "members_allowed_repository_creation_type": "all",
        "members_can_create_pages": True,
        "members_can_create_private_pages": True,
        "members_can_create_public_pages": True
    }


@pytest.fixture
def mock_api_responses():
    """Mock API responses for various endpoints"""
    return {
        "repositories": {
            "total_count": 1,
            "items": [
                {
                    "id": 123456789,
                    "name": "test-repo",
                    "full_name": "octocat/test-repo",
                    "description": "Test repository",
                    "private": False,
                    "html_url": "https://github.com/octocat/test-repo",
                    "language": "Python",
                    "stargazers_count": 10,
                    "score": 1.0
                }
            ]
        },
        "issues": {
            "total_count": 1,
            "items": [
                {
                    "id": 1,
                    "number": 1347,
                    "title": "Test issue",
                    "body": "This is a test issue",
                    "state": "open",
                    "html_url": "https://github.com/octocat/Hello-World/issues/1347",
                    "score": 1.0
                }
            ]
        },
        "users": {
            "total_count": 1,
            "items": [
                {
                    "id": 583231,
                    "login": "octocat",
                    "avatar_url": "https://github.com/images/error/octocat_happy.gif",
                    "html_url": "https://github.com/octocat",
                    "type": "User",
                    "score": 1.0
                }
            ]
        }
    }


# Async test utilities
@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Test markers
pytest_plugins = ["pytest_asyncio"]