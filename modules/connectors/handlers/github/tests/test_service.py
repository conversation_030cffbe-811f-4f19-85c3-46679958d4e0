"""
Tests for GitHub Connector Service
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import Dict, Any, List

from ..service import GitHubConnectorService
from ..schema import GitHubConfig
from ..constants.entities import GitHubEntityType
from ..constants.relationships import GitHubRelationshipType
from ...base_connector import ConnectorSearchResponse, SearchResultItem, SearchMetrics


class TestGitHubConnectorService:
    """Test cases for GitHub connector service"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "token": "test_token",
            "base_url": "https://api.github.com",
            "timeout": 30,
            "max_retries": 3,
            "rate_limit_buffer": 100,
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "pinecone": {
                "api_key": "test_pinecone_key",
                "environment": "test-env",
                "index_name": "test-index"
            }
        }

    @pytest.fixture
    def service(self, config):
        """Test service instance"""
        return GitHubConnectorService(config)

    def test_init(self, config):
        """Test service initialization"""
        service = GitHubConnectorService(config)
        assert service.config is not None
        assert service.connection_handler is not None
        assert service.source_type == "github"

    @pytest.mark.asyncio
    async def test_connect_success(self, service):
        """Test successful connection"""
        with patch.object(service.connection_handler, 'connect') as mock_connect:
            mock_connect.return_value = True
            
            result = await service.connect()
            assert result is True
            mock_connect.assert_called_once()

    @pytest.mark.asyncio
    async def test_connect_failure(self, service):
        """Test connection failure"""
        with patch.object(service.connection_handler, 'connect') as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            result = await service.connect()
            assert result is False

    def test_get_connector(self, service):
        """Test get connector method"""
        connector = service.get_connector()
        assert connector == service.connection_handler

    @pytest.mark.asyncio
    async def test_fetch_data_repositories(self, service):
        """Test fetching repository data"""
        mock_repos = [
            {
                "id": 1,
                "name": "test-repo",
                "full_name": "owner/test-repo",
                "description": "Test repository",
                "private": False,
                "html_url": "https://github.com/owner/test-repo",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "language": "Python",
                "stargazers_count": 10,
                "forks_count": 5,
                "open_issues_count": 2
            }
        ]
        
        with patch.object(service, '_fetch_repositories') as mock_fetch:
            mock_fetch.return_value = mock_repos
            
            result = await service.fetch_data("repositories")
            assert len(result) == 1
            assert result[0]["name"] == "test-repo"

    @pytest.mark.asyncio
    async def test_fetch_data_by_id_repository(self, service):
        """Test fetching repository by ID"""
        mock_repo = {
            "id": 1,
            "name": "test-repo",
            "full_name": "owner/test-repo",
            "description": "Test repository"
        }
        
        with patch.object(service.connection_handler, 'make_request') as mock_request:
            mock_request.return_value = mock_repo
            
            result = await service.fetch_data_by_id("repositories", "1")
            assert result["name"] == "test-repo"
            mock_request.assert_called_once_with("GET", "/repos/1")

    @pytest.mark.asyncio
    async def test_sync_repositories(self, service):
        """Test syncing repositories"""
        mock_repos = [
            {
                "id": 1,
                "name": "test-repo",
                "full_name": "owner/test-repo",
                "description": "Test repository"
            }
        ]
        
        with patch.object(service, '_fetch_repositories') as mock_fetch:
            with patch.object(service, '_store_repositories') as mock_store:
                mock_fetch.return_value = mock_repos
                mock_store.return_value = {"stored": 1, "updated": 0, "errors": 0}
                
                result = await service.sync("repositories")
                assert result["stored"] == 1
                mock_fetch.assert_called_once()
                mock_store.assert_called_once_with(mock_repos)

    @pytest.mark.asyncio
    async def test_sync_by_id(self, service):
        """Test syncing by ID"""
        mock_repo = {
            "id": 1,
            "name": "test-repo",
            "full_name": "owner/test-repo"
        }
        
        with patch.object(service, 'fetch_data_by_id') as mock_fetch:
            with patch.object(service, '_store_single_item') as mock_store:
                mock_fetch.return_value = mock_repo
                mock_store.return_value = {"stored": 1, "updated": 0, "errors": 0}
                
                result = await service.sync_by_id("repositories", "1")
                assert result["stored"] == 1

    @pytest.mark.asyncio
    async def test_store_context(self, service):
        """Test storing context"""
        context_data = {
            "repositories": [{"id": 1, "name": "test-repo"}],
            "issues": [{"id": 1, "title": "Test issue"}]
        }
        
        with patch.object(service, '_store_context_data') as mock_store:
            mock_store.return_value = {"stored": 2, "errors": 0}
            
            result = await service.store_context(context_data)
            assert result["stored"] == 2

    @pytest.mark.asyncio
    async def test_search_repositories(self, service):
        """Test repository search"""
        mock_results = [
            {
                "id": 1,
                "name": "test-repo",
                "full_name": "owner/test-repo",
                "description": "Test repository",
                "score": 1.0
            }
        ]
        
        with patch.object(service.connection_handler, 'make_request') as mock_request:
            mock_request.return_value = {
                "total_count": 1,
                "items": mock_results
            }
            
            result = await service.search("repositories", "test")
            assert isinstance(result, ConnectorSearchResponse)
            assert len(result.results) == 1
            assert result.results[0].title == "test-repo"
            assert result.metrics.total_results == 1

    @pytest.mark.asyncio
    async def test_search_issues(self, service):
        """Test issue search"""
        mock_results = [
            {
                "id": 1,
                "title": "Test issue",
                "body": "This is a test issue",
                "state": "open",
                "html_url": "https://github.com/owner/repo/issues/1",
                "score": 1.0
            }
        ]
        
        with patch.object(service.connection_handler, 'make_request') as mock_request:
            mock_request.return_value = {
                "total_count": 1,
                "items": mock_results
            }
            
            result = await service.search("issues", "test")
            assert isinstance(result, ConnectorSearchResponse)
            assert len(result.results) == 1
            assert result.results[0].title == "Test issue"

    @pytest.mark.asyncio
    async def test_search_invalid_entity_type(self, service):
        """Test search with invalid entity type"""
        result = await service.search("invalid_type", "test")
        assert isinstance(result, ConnectorSearchResponse)
        assert len(result.results) == 0
        assert len(result.errors) == 1

    def test_parse_query_simple(self, service):
        """Test simple query parsing"""
        query = "python repository"
        parsed = service._parse_query(query, "repositories")
        
        assert parsed["query"] == query
        assert parsed["entity_type"] == "repositories"
        assert parsed["filters"] == {}

    def test_parse_query_with_filters(self, service):
        """Test query parsing with filters"""
        query = "language:python stars:>10 user:octocat"
        parsed = service._parse_query(query, "repositories")
        
        assert "language:python" in parsed["query"]
        assert "stars:>10" in parsed["query"]
        assert "user:octocat" in parsed["query"]

    def test_format_search_results_repositories(self, service):
        """Test formatting repository search results"""
        raw_results = [
            {
                "id": 1,
                "name": "test-repo",
                "full_name": "owner/test-repo",
                "description": "Test repository",
                "html_url": "https://github.com/owner/test-repo",
                "language": "Python",
                "stargazers_count": 10,
                "score": 1.0
            }
        ]
        
        formatted = service._format_search_results(raw_results, "repositories")
        assert len(formatted) == 1
        
        result = formatted[0]
        assert result.title == "test-repo"
        assert result.description == "Test repository"
        assert result.url == "https://github.com/owner/test-repo"
        assert result.score == 1.0
        assert "Language: Python" in result.metadata["details"]

    def test_format_search_results_issues(self, service):
        """Test formatting issue search results"""
        raw_results = [
            {
                "id": 1,
                "title": "Test issue",
                "body": "This is a test issue",
                "state": "open",
                "html_url": "https://github.com/owner/repo/issues/1",
                "repository_url": "https://api.github.com/repos/owner/repo",
                "score": 1.0
            }
        ]
        
        formatted = service._format_search_results(raw_results, "issues")
        assert len(formatted) == 1
        
        result = formatted[0]
        assert result.title == "Test issue"
        assert result.description == "This is a test issue"
        assert result.url == "https://github.com/owner/repo/issues/1"
        assert result.metadata["state"] == "open"

    @pytest.mark.asyncio
    async def test_fetch_repositories(self, service):
        """Test fetching repositories"""
        mock_repos = [
            {"id": 1, "name": "repo1"},
            {"id": 2, "name": "repo2"}
        ]
        
        with patch.object(service.connection_handler, 'paginated_request') as mock_paginated:
            async def mock_generator():
                yield mock_repos
            
            mock_paginated.return_value = mock_generator()
            
            result = await service._fetch_repositories()
            assert len(result) == 2
            assert result[0]["name"] == "repo1"

    @pytest.mark.asyncio
    async def test_fetch_issues(self, service):
        """Test fetching issues"""
        mock_issues = [
            {"id": 1, "title": "Issue 1"},
            {"id": 2, "title": "Issue 2"}
        ]
        
        with patch.object(service.connection_handler, 'paginated_request') as mock_paginated:
            async def mock_generator():
                yield mock_issues
            
            mock_paginated.return_value = mock_generator()
            
            result = await service._fetch_issues()
            assert len(result) == 2
            assert result[0]["title"] == "Issue 1"

    def test_get_entity_endpoints(self, service):
        """Test getting entity endpoints"""
        endpoints = service._get_entity_endpoints()
        
        assert "repositories" in endpoints
        assert "issues" in endpoints
        assert "pull_requests" in endpoints
        assert "users" in endpoints
        
        assert endpoints["repositories"] == "/user/repos"
        assert endpoints["issues"] == "/issues"

    def test_get_search_endpoints(self, service):
        """Test getting search endpoints"""
        endpoints = service._get_search_endpoints()
        
        assert "repositories" in endpoints
        assert "issues" in endpoints
        assert "code" in endpoints
        assert "users" in endpoints
        
        assert endpoints["repositories"] == "/search/repositories"
        assert endpoints["issues"] == "/search/issues"

    @pytest.mark.asyncio
    async def test_error_handling(self, service):
        """Test error handling in various methods"""
        with patch.object(service.connection_handler, 'make_request') as mock_request:
            mock_request.side_effect = Exception("API Error")
            
            # Test fetch_data error handling
            result = await service.fetch_data("repositories")
            assert result == []
            
            # Test search error handling
            search_result = await service.search("repositories", "test")
            assert len(search_result.errors) == 1
            assert "API Error" in search_result.errors[0].message

    def test_supported_entity_types(self, service):
        """Test supported entity types"""
        supported = service._get_supported_entity_types()
        
        expected_types = [
            "repositories", "issues", "pull_requests", "commits",
            "users", "organizations", "teams", "projects",
            "releases", "workflows", "branches", "tags"
        ]
        
        for entity_type in expected_types:
            assert entity_type in supported

    def test_validate_entity_type(self, service):
        """Test entity type validation"""
        assert service._validate_entity_type("repositories") is True
        assert service._validate_entity_type("issues") is True
        assert service._validate_entity_type("invalid_type") is False

    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, service):
        """Test rate limit handling during operations"""
        with patch.object(service.connection_handler, 'make_request') as mock_request:
            # Simulate rate limit response
            mock_request.side_effect = [
                Exception("Rate limit exceeded"),
                {"id": 1, "name": "test-repo"}  # Success after retry
            ]
            
            with patch('asyncio.sleep'):  # Mock sleep to speed up test
                result = await service.fetch_data_by_id("repositories", "1")
                assert result["name"] == "test-repo"
                assert mock_request.call_count == 2