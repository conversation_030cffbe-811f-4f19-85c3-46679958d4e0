"""
Tests for GitHub Schema Validation
"""

import pytest
from pydantic import ValidationError
from datetime import datetime

from ..schema import (
    GitHubConfig,
    GitHubRepository,
    GitHubUser,
    GitHubIssue,
    GitHubPullRequest,
    GitHubCommit,
    GitHubOrganization,
    GitHubTeam,
    GitHubProject,
    GitHubRelease,
    GitHubWorkflow,
    GitHubBranch,
    GitHubTag
)


class TestGitHubConfig:
    """Test GitHub configuration schema"""

    def test_valid_config(self):
        """Test valid configuration"""
        config_data = {
            "token": "ghp_test_token",
            "base_url": "https://api.github.com",
            "timeout": 30,
            "max_retries": 3,
            "rate_limit_buffer": 100,
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "pinecone": {
                "api_key": "test_key",
                "environment": "test-env",
                "index_name": "test-index"
            }
        }
        
        config = GitHubConfig(**config_data)
        assert config.token == "ghp_test_token"
        assert config.base_url == "https://api.github.com"
        assert config.timeout == 30

    def test_missing_required_fields(self):
        """Test missing required fields"""
        with pytest.raises(ValidationError):
            GitHubConfig()

    def test_invalid_url(self):
        """Test invalid URL format"""
        with pytest.raises(ValidationError):
            GitHubConfig(
                token="test_token",
                base_url="invalid_url"
            )

    def test_negative_timeout(self):
        """Test negative timeout value"""
        with pytest.raises(ValidationError):
            GitHubConfig(
                token="test_token",
                base_url="https://api.github.com",
                timeout=-1
            )


class TestGitHubRepository:
    """Test GitHub repository schema"""

    def test_valid_repository(self):
        """Test valid repository data"""
        repo_data = {
            "id": 123456789,
            "node_id": "MDEwOlJlcG9zaXRvcnkxMjM0NTY3ODk=",
            "name": "test-repo",
            "full_name": "octocat/test-repo",
            "description": "A test repository",
            "private": False,
            "html_url": "https://github.com/octocat/test-repo",
            "clone_url": "https://github.com/octocat/test-repo.git",
            "ssh_url": "**************:octocat/test-repo.git",
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-02T00:00:00Z",
            "pushed_at": "2023-01-02T12:00:00Z",
            "language": "Python",
            "size": 1024,
            "stargazers_count": 42,
            "watchers_count": 42,
            "forks_count": 10,
            "open_issues_count": 5,
            "default_branch": "main",
            "archived": False,
            "disabled": False,
            "visibility": "public"
        }
        
        repo = GitHubRepository(**repo_data)
        assert repo.id == 123456789
        assert repo.name == "test-repo"
        assert repo.language == "Python"
        assert repo.stargazers_count == 42

    def test_minimal_repository(self):
        """Test repository with minimal required fields"""
        repo_data = {
            "id": 123,
            "name": "minimal-repo",
            "full_name": "user/minimal-repo",
            "private": False,
            "html_url": "https://github.com/user/minimal-repo"
        }
        
        repo = GitHubRepository(**repo_data)
        assert repo.id == 123
        assert repo.name == "minimal-repo"

    def test_invalid_repository_id(self):
        """Test invalid repository ID"""
        with pytest.raises(ValidationError):
            GitHubRepository(
                id="invalid_id",
                name="test-repo",
                full_name="user/test-repo",
                private=False,
                html_url="https://github.com/user/test-repo"
            )


class TestGitHubUser:
    """Test GitHub user schema"""

    def test_valid_user(self):
        """Test valid user data"""
        user_data = {
            "id": 12345,
            "node_id": "MDQ6VXNlcjEyMzQ1",
            "login": "octocat",
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/octocat",
            "type": "User",
            "name": "The Octocat",
            "company": "GitHub",
            "blog": "https://github.com/blog",
            "location": "San Francisco",
            "email": "<EMAIL>",
            "bio": "There once was...",
            "public_repos": 2,
            "public_gists": 1,
            "followers": 20,
            "following": 0,
            "created_at": "2008-01-14T04:33:35Z",
            "updated_at": "2008-01-14T04:33:35Z"
        }
        
        user = GitHubUser(**user_data)
        assert user.id == 12345
        assert user.login == "octocat"
        assert user.name == "The Octocat"
        assert user.public_repos == 2

    def test_organization_user(self):
        """Test organization type user"""
        user_data = {
            "id": 12345,
            "login": "github",
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/github",
            "type": "Organization"
        }
        
        user = GitHubUser(**user_data)
        assert user.type == "Organization"


class TestGitHubIssue:
    """Test GitHub issue schema"""

    def test_valid_issue(self):
        """Test valid issue data"""
        issue_data = {
            "id": 1,
            "node_id": "MDU6SXNzdWUx",
            "number": 1347,
            "title": "Found a bug",
            "body": "I'm having a problem with this.",
            "state": "open",
            "locked": False,
            "html_url": "https://github.com/octocat/Hello-World/issues/1347",
            "repository_url": "https://api.github.com/repos/octocat/Hello-World",
            "labels": [
                {
                    "id": 208045946,
                    "name": "bug",
                    "description": "Something isn't working",
                    "color": "d73a4a"
                }
            ],
            "assignees": [],
            "milestone": None,
            "comments": 0,
            "created_at": "2011-04-22T13:33:48Z",
            "updated_at": "2011-04-22T13:33:48Z",
            "closed_at": None,
            "author_association": "COLLABORATOR"
        }
        
        issue = GitHubIssue(**issue_data)
        assert issue.id == 1
        assert issue.number == 1347
        assert issue.title == "Found a bug"
        assert issue.state == "open"
        assert len(issue.labels) == 1

    def test_closed_issue(self):
        """Test closed issue"""
        issue_data = {
            "id": 1,
            "number": 1347,
            "title": "Fixed bug",
            "state": "closed",
            "html_url": "https://github.com/octocat/Hello-World/issues/1347",
            "repository_url": "https://api.github.com/repos/octocat/Hello-World",
            "created_at": "2011-04-22T13:33:48Z",
            "updated_at": "2011-04-22T13:33:48Z",
            "closed_at": "2011-04-23T13:33:48Z"
        }
        
        issue = GitHubIssue(**issue_data)
        assert issue.state == "closed"
        assert issue.closed_at is not None


class TestGitHubPullRequest:
    """Test GitHub pull request schema"""

    def test_valid_pull_request(self):
        """Test valid pull request data"""
        pr_data = {
            "id": 1,
            "node_id": "MDExOlB1bGxSZXF1ZXN0MQ==",
            "number": 1347,
            "title": "New feature",
            "body": "Please pull these awesome changes",
            "state": "open",
            "locked": False,
            "html_url": "https://github.com/octocat/Hello-World/pull/1347",
            "diff_url": "https://github.com/octocat/Hello-World/pull/1347.diff",
            "patch_url": "https://github.com/octocat/Hello-World/pull/1347.patch",
            "merged": False,
            "mergeable": True,
            "mergeable_state": "clean",
            "draft": False,
            "commits": 3,
            "additions": 100,
            "deletions": 3,
            "changed_files": 5,
            "created_at": "2011-01-26T19:01:12Z",
            "updated_at": "2011-01-26T19:14:43Z",
            "closed_at": None,
            "merged_at": None
        }
        
        pr = GitHubPullRequest(**pr_data)
        assert pr.id == 1
        assert pr.number == 1347
        assert pr.title == "New feature"
        assert pr.state == "open"
        assert pr.commits == 3
        assert pr.additions == 100

    def test_merged_pull_request(self):
        """Test merged pull request"""
        pr_data = {
            "id": 1,
            "number": 1347,
            "title": "Merged feature",
            "state": "closed",
            "html_url": "https://github.com/octocat/Hello-World/pull/1347",
            "merged": True,
            "created_at": "2011-01-26T19:01:12Z",
            "updated_at": "2011-01-26T19:14:43Z",
            "closed_at": "2011-01-26T19:15:00Z",
            "merged_at": "2011-01-26T19:15:00Z"
        }
        
        pr = GitHubPullRequest(**pr_data)
        assert pr.merged is True
        assert pr.merged_at is not None


class TestGitHubCommit:
    """Test GitHub commit schema"""

    def test_valid_commit(self):
        """Test valid commit data"""
        commit_data = {
            "sha": "6dcb09b5b57875f334f61aebed695e2e4193db5e",
            "node_id": "MDY6Q29tbWl0NmRjYjA5YjViNTc4NzVmMzM0ZjYxYWViZWQ2OTVlMmU0MTkzZGI1ZQ==",
            "html_url": "https://github.com/octocat/Hello-World/commit/6dcb09b5b57875f334f61aebed695e2e4193db5e",
            "comments_url": "https://api.github.com/repos/octocat/Hello-World/commits/6dcb09b5b57875f334f61aebed695e2e4193db5e/comments",
            "commit": {
                "message": "Fix all the bugs",
                "tree": {
                    "sha": "827efc6d56897b048c772eb4087f854f46256132",
                    "url": "https://api.github.com/repos/octocat/Hello-World/git/trees/827efc6d56897b048c772eb4087f854f46256132"
                },
                "author": {
                    "name": "Monalisa Octocat",
                    "email": "<EMAIL>",
                    "date": "2011-04-14T16:00:49Z"
                },
                "committer": {
                    "name": "Monalisa Octocat",
                    "email": "<EMAIL>",
                    "date": "2011-04-14T16:00:49Z"
                }
            },
            "stats": {
                "additions": 104,
                "deletions": 4,
                "total": 108
            }
        }
        
        commit = GitHubCommit(**commit_data)
        assert commit.sha == "6dcb09b5b57875f334f61aebed695e2e4193db5e"
        assert commit.commit["message"] == "Fix all the bugs"
        assert commit.stats["additions"] == 104


class TestGitHubOrganization:
    """Test GitHub organization schema"""

    def test_valid_organization(self):
        """Test valid organization data"""
        org_data = {
            "id": 1,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjE=",
            "login": "github",
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "description": "A great organization",
            "name": "GitHub",
            "company": None,
            "blog": "https://github.com/blog",
            "location": "San Francisco",
            "email": "<EMAIL>",
            "html_url": "https://github.com/github",
            "created_at": "2008-01-14T04:33:35Z",
            "updated_at": "2014-03-03T18:58:10Z",
            "type": "Organization",
            "public_repos": 2,
            "public_gists": 1,
            "followers": 20,
            "following": 0
        }
        
        org = GitHubOrganization(**org_data)
        assert org.id == 1
        assert org.login == "github"
        assert org.name == "GitHub"
        assert org.type == "Organization"


class TestGitHubWorkflow:
    """Test GitHub workflow schema"""

    def test_valid_workflow(self):
        """Test valid workflow data"""
        workflow_data = {
            "id": 161335,
            "node_id": "MDg6V29ya2Zsb3cxNjEzMzU=",
            "name": "CI",
            "path": ".github/workflows/blank.yml",
            "state": "active",
            "created_at": "2020-01-08T23:48:37.000Z",
            "updated_at": "2020-01-08T23:50:21.000Z",
            "url": "https://api.github.com/repos/octocat/Hello-World/actions/workflows/161335",
            "html_url": "https://github.com/octocat/Hello-World/blob/master/.github/workflows/161335",
            "badge_url": "https://github.com/octocat/Hello-World/workflows/CI/badge.svg"
        }
        
        workflow = GitHubWorkflow(**workflow_data)
        assert workflow.id == 161335
        assert workflow.name == "CI"
        assert workflow.state == "active"
        assert workflow.path == ".github/workflows/blank.yml"


class TestSchemaValidation:
    """Test general schema validation"""

    def test_datetime_parsing(self):
        """Test datetime field parsing"""
        repo_data = {
            "id": 123,
            "name": "test-repo",
            "full_name": "user/test-repo",
            "private": False,
            "html_url": "https://github.com/user/test-repo",
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-02T12:30:45.123Z"
        }
        
        repo = GitHubRepository(**repo_data)
        assert isinstance(repo.created_at, datetime)
        assert isinstance(repo.updated_at, datetime)

    def test_optional_fields(self):
        """Test handling of optional fields"""
        user_data = {
            "id": 123,
            "login": "testuser",
            "avatar_url": "https://github.com/images/error/octocat_happy.gif",
            "html_url": "https://github.com/testuser",
            "type": "User"
        }
        
        user = GitHubUser(**user_data)
        assert user.name is None
        assert user.email is None
        assert user.company is None

    def test_list_fields(self):
        """Test list field validation"""
        issue_data = {
            "id": 1,
            "number": 1347,
            "title": "Test issue",
            "state": "open",
            "html_url": "https://github.com/octocat/Hello-World/issues/1347",
            "repository_url": "https://api.github.com/repos/octocat/Hello-World",
            "labels": [
                {"id": 1, "name": "bug", "color": "red"},
                {"id": 2, "name": "enhancement", "color": "blue"}
            ],
            "assignees": []
        }
        
        issue = GitHubIssue(**issue_data)
        assert len(issue.labels) == 2
        assert len(issue.assignees) == 0
        assert issue.labels[0]["name"] == "bug"