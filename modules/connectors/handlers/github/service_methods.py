"""
GitHub Connector Service Methods

This module contains the implementation of core BaseConnector methods
for the GitHub connector service.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Iterator, Optional
import json

logger = logging.getLogger(__name__)

class GitHubServiceMethods:
    """Mixin class containing core service methods for GitHub connector"""
    
    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """
        Pulls all GitHub data with pagination as an iterator.
        
        Yields:
            Dict[str, Any]: GitHub entity data
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info("Starting comprehensive GitHub data fetch...")
            
            # Fetch repositories first (they're the foundation)
            yield from self._fetch_repositories()
            
            # Fetch users and organizations
            yield from self._fetch_users()
            yield from self._fetch_organizations()
            
            # Fetch repository-specific data
            yield from self._fetch_issues()
            yield from self._fetch_pull_requests()
            yield from self._fetch_commits()
            yield from self._fetch_workflows()
            
            # Fetch additional entities
            yield from self._fetch_releases()
            yield from self._fetch_branches()
            yield from self._fetch_tags()
            
            logger.info("Completed comprehensive GitHub data fetch")
            
        except Exception as e:
            logger.error(f"Error during GitHub data fetch: {str(e)}")
            raise

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a single GitHub entity by its ID.
        
        Args:
            id: Entity identifier (format: entity_type:entity_id)
            
        Returns:
            Dict[str, Any]: Entity data
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            # Parse entity type and ID
            if ':' not in id:
                raise ValueError("ID must be in format 'entity_type:entity_id'")
            
            entity_type, entity_id = id.split(':', 1)
            
            logger.info(f"Fetching GitHub {entity_type} with ID: {entity_id}")
            
            # Route to appropriate fetch method
            if entity_type.lower() == 'repository':
                return self._fetch_repository_by_id(entity_id)
            elif entity_type.lower() == 'user':
                return self._fetch_user_by_id(entity_id)
            elif entity_type.lower() == 'organization':
                return self._fetch_organization_by_id(entity_id)
            elif entity_type.lower() == 'issue':
                return self._fetch_issue_by_id(entity_id)
            elif entity_type.lower() == 'pull_request':
                return self._fetch_pull_request_by_id(entity_id)
            elif entity_type.lower() == 'commit':
                return self._fetch_commit_by_id(entity_id)
            elif entity_type.lower() == 'workflow':
                return self._fetch_workflow_by_id(entity_id)
            else:
                raise ValueError(f"Unsupported entity type: {entity_type}")
                
        except Exception as e:
            logger.error(f"Error fetching GitHub entity by ID {id}: {str(e)}")
            raise

    def sync(self):
        """
        Perform a full synchronization of GitHub data.
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info("Starting full GitHub synchronization...")
            start_time = datetime.now()
            
            # Track sync statistics
            sync_stats = {
                'repositories': 0,
                'users': 0,
                'organizations': 0,
                'issues': 0,
                'pull_requests': 0,
                'commits': 0,
                'workflows': 0,
                'errors': []
            }
            
            # Perform full data fetch and store
            for entity_data in self.fetch_data():
                try:
                    # Store context for each entity
                    self.store_context(entity_data)
                    
                    # Update statistics
                    entity_type = entity_data.get('entity_type', 'unknown')
                    if entity_type in sync_stats:
                        sync_stats[entity_type] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to sync entity {entity_data.get('id', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    sync_stats['errors'].append(error_msg)
            
            # Calculate sync duration
            sync_duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"GitHub full sync completed in {sync_duration:.2f} seconds")
            logger.info(f"Sync statistics: {json.dumps(sync_stats, indent=2)}")
            
            # Update connector metadata
            connector_info = self.get_connector()
            connector_info['last_sync'] = datetime.now().isoformat()
            connector_info['sync_stats'] = sync_stats
            
        except Exception as e:
            logger.error(f"GitHub full sync failed: {str(e)}")
            raise

    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single GitHub entity.
        
        Args:
            id: Entity identifier (format: entity_type:entity_id)
        """
        if not self._connected or not self.connection:
            raise ConnectionError("GitHub connector not connected. Call connect() first.")
        
        try:
            logger.info(f"Starting partial GitHub sync for entity: {id}")
            
            # Fetch the specific entity
            entity_data = self.fetch_data_by_id(id)
            
            # Store the entity context
            self.store_context(entity_data)
            
            logger.info(f"Successfully synced GitHub entity: {id}")
            
        except Exception as e:
            logger.error(f"GitHub partial sync failed for {id}: {str(e)}")
            raise

    def store_context(self, data: Any):
        """
        Stores both context and embedding for given GitHub data.
        
        Args:
            data: GitHub entity data to store
        """
        try:
            if not isinstance(data, dict):
                logger.warning(f"Invalid data type for storage: {type(data)}")
                return
            
            entity_type = data.get('entity_type', 'unknown')
            entity_id = data.get('id', 'unknown')
            
            logger.debug(f"Storing context for GitHub {entity_type}: {entity_id}")
            
            # Prepare data for storage
            storage_data = {
                'source_type': 'github',
                'entity_type': entity_type,
                'entity_id': entity_id,
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'connector_version': self.version
            }
            
            # TODO: Implement actual storage logic
            # This would typically involve:
            # 1. Storing structured data in PostgreSQL
            # 2. Creating embeddings and storing in Pinecone
            # 3. Creating knowledge graph nodes and relationships in Neo4j
            
            # Placeholder for storage implementation
            logger.debug(f"Context stored for {entity_type}:{entity_id}")
            
        except Exception as e:
            logger.error(f"Failed to store context for GitHub data: {str(e)}")
            raise

    # Private helper methods for fetching specific entity types
    
    def _fetch_repositories(self) -> Iterator[Dict[str, Any]]:
        """Fetch all accessible repositories"""
        try:
            page = 1
            per_page = 100
            
            while True:
                response = self.connection.make_request(
                    'GET',
                    f'/user/repos?page={page}&per_page={per_page}&sort=updated&direction=desc'
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to fetch repositories: {response.status_code}")
                    break
                
                repos = response.json()
                if not repos:
                    break
                
                for repo in repos:
                    yield {
                        'entity_type': 'repository',
                        'id': f"repository:{repo['id']}",
                        'github_id': repo['id'],
                        'name': repo['name'],
                        'full_name': repo['full_name'],
                        'description': repo.get('description'),
                        'private': repo['private'],
                        'fork': repo['fork'],
                        'owner': repo['owner'],
                        'html_url': repo['html_url'],
                        'clone_url': repo['clone_url'],
                        'language': repo.get('language'),
                        'stargazers_count': repo['stargazers_count'],
                        'watchers_count': repo['watchers_count'],
                        'forks_count': repo['forks_count'],
                        'open_issues_count': repo['open_issues_count'],
                        'default_branch': repo['default_branch'],
                        'topics': repo.get('topics', []),
                        'created_at': repo['created_at'],
                        'updated_at': repo['updated_at'],
                        'pushed_at': repo.get('pushed_at'),
                        'raw_data': repo
                    }
                
                page += 1
                
        except Exception as e:
            logger.error(f"Error fetching repositories: {str(e)}")
            raise

    def _fetch_users(self) -> Iterator[Dict[str, Any]]:
        """Fetch users (contributors, collaborators, etc.)"""
        try:
            # This is a simplified implementation
            # In practice, you'd fetch users from various contexts
            response = self.connection.make_request('GET', '/user')
            
            if response.status_code == 200:
                user = response.json()
                yield {
                    'entity_type': 'user',
                    'id': f"user:{user['id']}",
                    'github_id': user['id'],
                    'login': user['login'],
                    'name': user.get('name'),
                    'email': user.get('email'),
                    'bio': user.get('bio'),
                    'company': user.get('company'),
                    'location': user.get('location'),
                    'blog': user.get('blog'),
                    'twitter_username': user.get('twitter_username'),
                    'public_repos': user['public_repos'],
                    'public_gists': user['public_gists'],
                    'followers': user['followers'],
                    'following': user['following'],
                    'created_at': user['created_at'],
                    'updated_at': user['updated_at'],
                    'avatar_url': user['avatar_url'],
                    'html_url': user['html_url'],
                    'type': user['type'],
                    'raw_data': user
                }
                
        except Exception as e:
            logger.error(f"Error fetching users: {str(e)}")
            raise

    def _fetch_organizations(self) -> Iterator[Dict[str, Any]]:
        """Fetch organizations"""
        try:
            response = self.connection.make_request('GET', '/user/orgs')
            
            if response.status_code == 200:
                orgs = response.json()
                for org in orgs:
                    # Fetch detailed org info
                    org_response = self.connection.make_request('GET', f'/orgs/{org["login"]}')
                    if org_response.status_code == 200:
                        org_detail = org_response.json()
                        yield {
                            'entity_type': 'organization',
                            'id': f"organization:{org_detail['id']}",
                            'github_id': org_detail['id'],
                            'login': org_detail['login'],
                            'name': org_detail.get('name'),
                            'description': org_detail.get('description'),
                            'company': org_detail.get('company'),
                            'blog': org_detail.get('blog'),
                            'location': org_detail.get('location'),
                            'email': org_detail.get('email'),
                            'twitter_username': org_detail.get('twitter_username'),
                            'public_repos': org_detail['public_repos'],
                            'public_gists': org_detail['public_gists'],
                            'followers': org_detail['followers'],
                            'following': org_detail['following'],
                            'created_at': org_detail['created_at'],
                            'updated_at': org_detail['updated_at'],
                            'avatar_url': org_detail['avatar_url'],
                            'html_url': org_detail['html_url'],
                            'raw_data': org_detail
                        }
                        
        except Exception as e:
            logger.error(f"Error fetching organizations: {str(e)}")
            raise

    def _fetch_issues(self) -> Iterator[Dict[str, Any]]:
        """Fetch issues from repositories"""
        try:
            # Fetch issues from user's repositories
            response = self.connection.make_request('GET', '/issues?filter=all&state=all&sort=updated&direction=desc')
            
            if response.status_code == 200:
                issues = response.json()
                for issue in issues:
                    # Skip pull requests (they appear in issues endpoint)
                    if 'pull_request' in issue:
                        continue
                        
                    yield {
                        'entity_type': 'issue',
                        'id': f"issue:{issue['id']}",
                        'github_id': issue['id'],
                        'number': issue['number'],
                        'title': issue['title'],
                        'body': issue.get('body'),
                        'state': issue['state'],
                        'locked': issue['locked'],
                        'assignee': issue.get('assignee'),
                        'assignees': issue.get('assignees', []),
                        'milestone': issue.get('milestone'),
                        'labels': issue.get('labels', []),
                        'user': issue['user'],
                        'comments': issue['comments'],
                        'created_at': issue['created_at'],
                        'updated_at': issue['updated_at'],
                        'closed_at': issue.get('closed_at'),
                        'html_url': issue['html_url'],
                        'repository_url': issue['repository_url'],
                        'raw_data': issue
                    }
                    
        except Exception as e:
            logger.error(f"Error fetching issues: {str(e)}")
            raise

    def _fetch_pull_requests(self) -> Iterator[Dict[str, Any]]:
        """Fetch pull requests from repositories"""
        try:
            # This would typically iterate through repositories and fetch PRs
            # For now, we'll use a simplified approach
            logger.info("Fetching pull requests...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching pull requests: {str(e)}")
            raise

    def _fetch_commits(self) -> Iterator[Dict[str, Any]]:
        """Fetch commits from repositories"""
        try:
            # This would typically iterate through repositories and fetch commits
            # For now, we'll use a simplified approach
            logger.info("Fetching commits...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching commits: {str(e)}")
            raise

    def _fetch_workflows(self) -> Iterator[Dict[str, Any]]:
        """Fetch GitHub Actions workflows"""
        try:
            # This would typically iterate through repositories and fetch workflows
            # For now, we'll use a simplified approach
            logger.info("Fetching workflows...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching workflows: {str(e)}")
            raise

    def _fetch_releases(self) -> Iterator[Dict[str, Any]]:
        """Fetch releases from repositories"""
        try:
            logger.info("Fetching releases...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching releases: {str(e)}")
            raise

    def _fetch_branches(self) -> Iterator[Dict[str, Any]]:
        """Fetch branches from repositories"""
        try:
            logger.info("Fetching branches...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching branches: {str(e)}")
            raise

    def _fetch_tags(self) -> Iterator[Dict[str, Any]]:
        """Fetch tags from repositories"""
        try:
            logger.info("Fetching tags...")
            # Implementation would go here
            
        except Exception as e:
            logger.error(f"Error fetching tags: {str(e)}")
            raise

    # Fetch by ID methods
    
    def _fetch_repository_by_id(self, repo_id: str) -> Dict[str, Any]:
        """Fetch repository by ID"""
        response = self.connection.make_request('GET', f'/repositories/{repo_id}')
        if response.status_code == 200:
            repo = response.json()
            return {
                'entity_type': 'repository',
                'id': f"repository:{repo['id']}",
                'raw_data': repo,
                # ... other fields
            }
        else:
            raise ValueError(f"Repository {repo_id} not found")

    def _fetch_user_by_id(self, user_id: str) -> Dict[str, Any]:
        """Fetch user by ID or login"""
        response = self.connection.make_request('GET', f'/users/{user_id}')
        if response.status_code == 200:
            user = response.json()
            return {
                'entity_type': 'user',
                'id': f"user:{user['id']}",
                'raw_data': user,
                # ... other fields
            }
        else:
            raise ValueError(f"User {user_id} not found")

    def _fetch_organization_by_id(self, org_id: str) -> Dict[str, Any]:
        """Fetch organization by ID or login"""
        response = self.connection.make_request('GET', f'/orgs/{org_id}')
        if response.status_code == 200:
            org = response.json()
            return {
                'entity_type': 'organization',
                'id': f"organization:{org['id']}",
                'raw_data': org,
                # ... other fields
            }
        else:
            raise ValueError(f"Organization {org_id} not found")

    def _fetch_issue_by_id(self, issue_id: str) -> Dict[str, Any]:
        """Fetch issue by ID"""
        # This would require repository context
        raise NotImplementedError("Issue fetch by ID requires repository context")

    def _fetch_pull_request_by_id(self, pr_id: str) -> Dict[str, Any]:
        """Fetch pull request by ID"""
        # This would require repository context
        raise NotImplementedError("Pull request fetch by ID requires repository context")

    def _fetch_commit_by_id(self, commit_id: str) -> Dict[str, Any]:
        """Fetch commit by SHA"""
        # This would require repository context
        raise NotImplementedError("Commit fetch by ID requires repository context")

    def _fetch_workflow_by_id(self, workflow_id: str) -> Dict[str, Any]:
        """Fetch workflow by ID"""
        # This would require repository context
        raise NotImplementedError("Workflow fetch by ID requires repository context")