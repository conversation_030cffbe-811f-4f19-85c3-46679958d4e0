from enum import Enum

class GitHubRelationshipType(Enum):
    """
    Comprehensive GitHub relationship types defining connections between entities.
    
    This enum defines all possible edge types that can be created in the knowledge graph
    for GitHub data relationships, following the structured connector pattern.
    """
    
    # Repository Ownership & Access
    OWNS_REPOSITORY = "OWNS_REPOSITORY"
    MEMBER_OF_ORGANIZATION = "MEMBER_OF_ORGANIZATION"
    ADMIN_OF_ORGANIZATION = "ADMIN_OF_ORGANIZATION"
    COLLABORATES_ON = "COLLABORATES_ON"
    HAS_REPOSITORY = "HAS_REPOSITORY"
    
    # Repository Interactions
    FORKS_FROM = "FORKS_FROM"
    STARS_REPOSITORY = "STARS_REPOSITORY"
    WATCHES_REPOSITORY = "WATCHES_REPOSITORY"
    SUBSCRIBES_TO = "SUBSCRIBES_TO"
    
    # Code & Development Relationships
    HAS_COMMIT = "HAS_COMMIT"
    HAS_BRANCH = "HAS_BRANCH"
    HAS_TAG = "HAS_TAG"
    HAS_RELEASE = "HAS_RELEASE"
    HAS_FILE = "HAS_FILE"
    HAS_TREE = "HAS_TREE"
    COMMITS_TO = "COMMITS_TO"
    AUTHORED_BY = "AUTHORED_BY"
    COMMITTED_BY = "COMMITTED_BY"
    MERGES_INTO = "MERGES_INTO"
    BRANCHES_FROM = "BRANCHES_FROM"
    TAGS_COMMIT = "TAGS_COMMIT"
    RELEASES_FROM = "RELEASES_FROM"
    
    # Issue & Pull Request Relationships
    HAS_ISSUE = "HAS_ISSUE"
    HAS_PULL_REQUEST = "HAS_PULL_REQUEST"
    ASSIGNED_TO = "ASSIGNED_TO"
    CREATED_BY = "CREATED_BY"
    CLOSED_BY = "CLOSED_BY"
    REOPENED_BY = "REOPENED_BY"
    MENTIONS = "MENTIONS"
    REFERENCES = "REFERENCES"
    CLOSES_ISSUE = "CLOSES_ISSUE"
    FIXES_ISSUE = "FIXES_ISSUE"
    RESOLVES_ISSUE = "RESOLVES_ISSUE"
    
    # Review & Comment Relationships
    HAS_REVIEW = "HAS_REVIEW"
    HAS_COMMENT = "HAS_COMMENT"
    HAS_REACTION = "HAS_REACTION"
    REVIEWS = "REVIEWS"
    COMMENTS_ON = "COMMENTS_ON"
    REACTS_TO = "REACTS_TO"
    APPROVES = "APPROVES"
    REQUESTS_CHANGES = "REQUESTS_CHANGES"
    DISMISSES_REVIEW = "DISMISSES_REVIEW"
    
    # Project Management Relationships
    HAS_MILESTONE = "HAS_MILESTONE"
    HAS_PROJECT = "HAS_PROJECT"
    HAS_PROJECT_CARD = "HAS_PROJECT_CARD"
    HAS_PROJECT_COLUMN = "HAS_PROJECT_COLUMN"
    HAS_LABEL = "HAS_LABEL"
    PART_OF_MILESTONE = "PART_OF_MILESTONE"
    PART_OF_PROJECT = "PART_OF_PROJECT"
    IN_COLUMN = "IN_COLUMN"
    LABELED_WITH = "LABELED_WITH"
    MOVED_TO_COLUMN = "MOVED_TO_COLUMN"
    
    # Team & Organization Relationships
    HAS_TEAM = "HAS_TEAM"
    MEMBER_OF_TEAM = "MEMBER_OF_TEAM"
    MAINTAINER_OF_TEAM = "MAINTAINER_OF_TEAM"
    TEAM_HAS_REPOSITORY_ACCESS = "TEAM_HAS_REPOSITORY_ACCESS"
    
    # CI/CD & Workflow Relationships
    HAS_WORKFLOW = "HAS_WORKFLOW"
    HAS_WORKFLOW_RUN = "HAS_WORKFLOW_RUN"
    HAS_JOB = "HAS_JOB"
    HAS_ACTION = "HAS_ACTION"
    HAS_CHECK_RUN = "HAS_CHECK_RUN"
    HAS_CHECK_SUITE = "HAS_CHECK_SUITE"
    TRIGGERS_WORKFLOW = "TRIGGERS_WORKFLOW"
    RUNS_WORKFLOW = "RUNS_WORKFLOW"
    EXECUTES_JOB = "EXECUTES_JOB"
    USES_ACTION = "USES_ACTION"
    CHECKS_COMMIT = "CHECKS_COMMIT"
    
    # Deployment & Environment Relationships
    HAS_DEPLOYMENT = "HAS_DEPLOYMENT"
    HAS_ENVIRONMENT = "HAS_ENVIRONMENT"
    HAS_SECRET = "HAS_SECRET"
    DEPLOYS_TO = "DEPLOYS_TO"
    DEPLOYED_FROM = "DEPLOYED_FROM"
    USES_ENVIRONMENT = "USES_ENVIRONMENT"
    ACCESSES_SECRET = "ACCESSES_SECRET"
    
    # Security Relationships
    HAS_WEBHOOK = "HAS_WEBHOOK"
    HAS_SECURITY_ADVISORY = "HAS_SECURITY_ADVISORY"
    HAS_VULNERABILITY = "HAS_VULNERABILITY"
    AFFECTS_REPOSITORY = "AFFECTS_REPOSITORY"
    REPORTS_VULNERABILITY = "REPORTS_VULNERABILITY"
    
    # Social & Community Relationships
    HAS_DISCUSSION = "HAS_DISCUSSION"
    HAS_GIST = "HAS_GIST"
    SPONSORS = "SPONSORS"
    SPONSORED_BY = "SPONSORED_BY"
    PARTICIPATES_IN = "PARTICIPATES_IN"
    FOLLOWS_USER = "FOLLOWS_USER"
    FOLLOWED_BY = "FOLLOWED_BY"
    
    # Repository Features Relationships
    HAS_WIKI = "HAS_WIKI"
    HAS_PAGES = "HAS_PAGES"
    EDITS_WIKI = "EDITS_WIKI"
    PUBLISHES_PAGES = "PUBLISHES_PAGES"
    
    # Notification & Event Relationships
    HAS_NOTIFICATION = "HAS_NOTIFICATION"
    HAS_EVENT = "HAS_EVENT"
    HAS_ACTIVITY = "HAS_ACTIVITY"
    NOTIFIES = "NOTIFIES"
    TRIGGERS_EVENT = "TRIGGERS_EVENT"
    PERFORMS_ACTIVITY = "PERFORMS_ACTIVITY"
    
    # Dependency & Link Relationships
    DEPENDS_ON = "DEPENDS_ON"
    LINKED_TO = "LINKED_TO"
    RELATES_TO = "RELATES_TO"
    BLOCKS = "BLOCKS"
    BLOCKED_BY = "BLOCKED_BY"
    DUPLICATES = "DUPLICATES"
    DUPLICATED_BY = "DUPLICATED_BY"

def get_all_relationship_types():
    """
    Returns a list of all GitHub relationship type values.
    
    This function is required by the connector registration system
    to dynamically load relationship definitions.
    
    Returns:
        List[str]: List of all relationship type string values
    """
    return [relationship.value for relationship in GitHubRelationshipType]