from enum import Enum

class EntityType(Enum):
    """
    Comprehensive GitHub entity types covering all major GitHub data structures.
    
    This enum defines all possible node types that can be created in the knowledge graph
    for GitHub data, following the structured connector pattern.
    """
    
    # Core Entities
    GITHUB_REPOSITORY = "GitHubRepository"
    GITHUB_USER = "GitHubUser"
    GITHUB_ORGANIZATION = "GitHubOrganization"
    
    # Code & Development
    GITHUB_COMMIT = "GitHubCommit"
    GITHUB_BRANCH = "GitHubBranch"
    GITHUB_TAG = "GitHubTag"
    GITHUB_RELEASE = "GitHubRelease"
    GITHUB_FILE = "GitHubFile"
    GITHUB_TREE = "GitHubTree"
    
    # Collaboration & Issues
    GITHUB_ISSUE = "GitHubIssue"
    GITHUB_PULL_REQUEST = "GitHubPullRequest"
    GITHUB_REVIEW = "GitHubReview"
    GITHUB_COMMENT = "GitHubComment"
    GITHUB_REACTION = "GitHubReaction"
    
    # Project Management
    GITHUB_MILESTONE = "GitHubMilestone"
    GITHUB_PROJECT = "GitHubProject"
    GITHUB_PROJECT_CARD = "GitHubProjectCard"
    GITHUB_PROJECT_COLUMN = "GitHubProjectColumn"
    GITHUB_LABEL = "GitHubLabel"
    
    # CI/CD & Automation
    GITHUB_WORKFLOW = "GitHubWorkflow"
    GITHUB_WORKFLOW_RUN = "GitHubWorkflowRun"
    GITHUB_JOB = "GitHubJob"
    GITHUB_ACTION = "GitHubAction"
    GITHUB_CHECK_RUN = "GitHubCheckRun"
    GITHUB_CHECK_SUITE = "GitHubCheckSuite"
    
    # Security & Management
    GITHUB_TEAM = "GitHubTeam"
    GITHUB_WEBHOOK = "GitHubWebhook"
    GITHUB_DEPLOYMENT = "GitHubDeployment"
    GITHUB_ENVIRONMENT = "GitHubEnvironment"

    GITHUB_SECRET = "GitHubSecret"
    
    # Social & Community
    GITHUB_DISCUSSION = "GitHubDiscussion"
    GITHUB_GIST = "GitHubGist"
    GITHUB_SPONSOR = "GitHubSponsor"
    
    # Repository Features
    GITHUB_WIKI = "GitHubWiki"
    GITHUB_PAGES = "GitHubPages"
    GITHUB_SECURITY_ADVISORY = "GitHubSecurityAdvisory"
    GITHUB_VULNERABILITY = "GitHubVulnerability"
    
    # Notifications & Events
    GITHUB_NOTIFICATION = "GitHubNotification"
    GITHUB_EVENT = "GitHubEvent"
    GITHUB_ACTIVITY = "GitHubActivity"
    
def get_all_entity_types():
    """
    Returns a set of all GitHub entity type values.
    
    This function is required by the connector registration system
    to dynamically load entity definitions.
    
    Returns:
        set: Set of all entity type string values
    """
    return {e.value for e in EntityType}