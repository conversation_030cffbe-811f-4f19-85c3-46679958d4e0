"""
GitHub Connector Pydantic Schemas

This module defines Pydantic schemas for data validation and serialization
of GitHub entities and API responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Configuration Schemas

class GitHubConfig(BaseModel):
    """Configuration schema for GitHub connector."""
    api_base_url: str = Field(default="https://api.github.com", description="GitHub API base URL")
    graphql_url: str = Field(default="https://api.github.com/graphql", description="GitHub GraphQL API URL")
    token: str = Field(..., description="GitHub Personal Access Token")
    webhook_secret: Optional[str] = Field(default=None, description="Webhook secret for validation")
    rate_limit_per_hour: int = Field(default=5000, description="Rate limit per hour")
    timeout_seconds: int = Field(default=30, description="Request timeout in seconds")
    
    class Config:
        extra = "allow"

class DatabaseConfig(BaseModel):
    """Database configuration schema."""
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")

class PineconeConfig(BaseModel):
    """Pinecone configuration schema."""
    api_key: str = Field(..., description="Pinecone API key")
    environment: str = Field(..., description="Pinecone environment")
    index_name: str = Field(..., description="Pinecone index name")
    dimension: int = Field(default=1536, description="Vector dimension")

# GitHub Entity Schemas

class GitHubUser(BaseModel):
    """GitHub user entity schema."""
    id: int = Field(..., description="GitHub user ID")
    login: str = Field(..., description="GitHub username")
    name: Optional[str] = Field(default=None, description="Full name")
    email: Optional[str] = Field(default=None, description="Email address")
    bio: Optional[str] = Field(default=None, description="User bio")
    company: Optional[str] = Field(default=None, description="Company")
    location: Optional[str] = Field(default=None, description="Location")
    blog: Optional[str] = Field(default=None, description="Blog URL")
    twitter_username: Optional[str] = Field(default=None, description="Twitter username")
    public_repos: int = Field(default=0, description="Number of public repositories")
    public_gists: int = Field(default=0, description="Number of public gists")
    followers: int = Field(default=0, description="Number of followers")
    following: int = Field(default=0, description="Number of following")
    created_at: datetime = Field(..., description="Account creation date")
    updated_at: datetime = Field(..., description="Last update date")
    avatar_url: Optional[str] = Field(default=None, description="Avatar URL")
    html_url: str = Field(..., description="GitHub profile URL")
    type: str = Field(default="User", description="User type")

class GitHubOrganization(BaseModel):
    """GitHub organization entity schema."""
    id: int = Field(..., description="GitHub organization ID")
    login: str = Field(..., description="Organization login")
    name: Optional[str] = Field(default=None, description="Organization name")
    description: Optional[str] = Field(default=None, description="Organization description")
    company: Optional[str] = Field(default=None, description="Company")
    blog: Optional[str] = Field(default=None, description="Blog URL")
    location: Optional[str] = Field(default=None, description="Location")
    email: Optional[str] = Field(default=None, description="Email address")
    twitter_username: Optional[str] = Field(default=None, description="Twitter username")
    public_repos: int = Field(default=0, description="Number of public repositories")
    public_gists: int = Field(default=0, description="Number of public gists")
    followers: int = Field(default=0, description="Number of followers")
    following: int = Field(default=0, description="Number of following")
    created_at: datetime = Field(..., description="Organization creation date")
    updated_at: datetime = Field(..., description="Last update date")
    avatar_url: Optional[str] = Field(default=None, description="Avatar URL")
    html_url: str = Field(..., description="GitHub organization URL")

class GitHubRepository(BaseModel):
    """GitHub repository entity schema."""
    id: int = Field(..., description="GitHub repository ID")
    name: str = Field(..., description="Repository name")
    full_name: str = Field(..., description="Full repository name (owner/repo)")
    description: Optional[str] = Field(default=None, description="Repository description")
    private: bool = Field(default=False, description="Is repository private")
    fork: bool = Field(default=False, description="Is repository a fork")
    archived: bool = Field(default=False, description="Is repository archived")
    disabled: bool = Field(default=False, description="Is repository disabled")
    owner: GitHubUser = Field(..., description="Repository owner")
    html_url: str = Field(..., description="Repository URL")
    clone_url: str = Field(..., description="Clone URL")
    git_url: str = Field(..., description="Git URL")
    ssh_url: str = Field(..., description="SSH URL")
    homepage: Optional[str] = Field(default=None, description="Homepage URL")
    language: Optional[str] = Field(default=None, description="Primary language")
    size: int = Field(default=0, description="Repository size in KB")
    stargazers_count: int = Field(default=0, description="Number of stars")
    watchers_count: int = Field(default=0, description="Number of watchers")
    forks_count: int = Field(default=0, description="Number of forks")
    open_issues_count: int = Field(default=0, description="Number of open issues")
    default_branch: str = Field(default="main", description="Default branch")
    topics: List[str] = Field(default_factory=list, description="Repository topics")
    license: Optional[Dict[str, Any]] = Field(default=None, description="License information")
    created_at: datetime = Field(..., description="Repository creation date")
    updated_at: datetime = Field(..., description="Last update date")
    pushed_at: Optional[datetime] = Field(default=None, description="Last push date")

class GitHubIssue(BaseModel):
    """GitHub issue entity schema."""
    id: int = Field(..., description="GitHub issue ID")
    number: int = Field(..., description="Issue number")
    title: str = Field(..., description="Issue title")
    body: Optional[str] = Field(default=None, description="Issue body")
    state: str = Field(..., description="Issue state (open/closed)")
    locked: bool = Field(default=False, description="Is issue locked")
    assignee: Optional[GitHubUser] = Field(default=None, description="Assigned user")
    assignees: List[GitHubUser] = Field(default_factory=list, description="Assigned users")
    milestone: Optional[Dict[str, Any]] = Field(default=None, description="Milestone information")
    labels: List[Dict[str, Any]] = Field(default_factory=list, description="Issue labels")
    user: GitHubUser = Field(..., description="Issue creator")
    comments: int = Field(default=0, description="Number of comments")
    created_at: datetime = Field(..., description="Issue creation date")
    updated_at: datetime = Field(..., description="Last update date")
    closed_at: Optional[datetime] = Field(default=None, description="Issue close date")
    html_url: str = Field(..., description="Issue URL")

class GitHubPullRequest(BaseModel):
    """GitHub pull request entity schema."""
    id: int = Field(..., description="GitHub pull request ID")
    number: int = Field(..., description="Pull request number")
    title: str = Field(..., description="Pull request title")
    body: Optional[str] = Field(default=None, description="Pull request body")
    state: str = Field(..., description="Pull request state")
    locked: bool = Field(default=False, description="Is pull request locked")
    merged: bool = Field(default=False, description="Is pull request merged")
    mergeable: Optional[bool] = Field(default=None, description="Is pull request mergeable")
    draft: bool = Field(default=False, description="Is pull request a draft")
    user: GitHubUser = Field(..., description="Pull request creator")
    assignee: Optional[GitHubUser] = Field(default=None, description="Assigned user")
    assignees: List[GitHubUser] = Field(default_factory=list, description="Assigned users")
    requested_reviewers: List[GitHubUser] = Field(default_factory=list, description="Requested reviewers")
    milestone: Optional[Dict[str, Any]] = Field(default=None, description="Milestone information")
    labels: List[Dict[str, Any]] = Field(default_factory=list, description="Pull request labels")
    head: Dict[str, Any] = Field(..., description="Head branch information")
    base: Dict[str, Any] = Field(..., description="Base branch information")
    commits: int = Field(default=0, description="Number of commits")
    additions: int = Field(default=0, description="Number of additions")
    deletions: int = Field(default=0, description="Number of deletions")
    changed_files: int = Field(default=0, description="Number of changed files")
    comments: int = Field(default=0, description="Number of comments")
    review_comments: int = Field(default=0, description="Number of review comments")
    created_at: datetime = Field(..., description="Pull request creation date")
    updated_at: datetime = Field(..., description="Last update date")
    closed_at: Optional[datetime] = Field(default=None, description="Pull request close date")
    merged_at: Optional[datetime] = Field(default=None, description="Pull request merge date")
    html_url: str = Field(..., description="Pull request URL")

class GitHubCommit(BaseModel):
    """GitHub commit entity schema."""
    sha: str = Field(..., description="Commit SHA")
    message: str = Field(..., description="Commit message")
    author: Dict[str, Any] = Field(..., description="Commit author")
    committer: Dict[str, Any] = Field(..., description="Commit committer")
    tree: Dict[str, Any] = Field(..., description="Tree information")
    parents: List[Dict[str, Any]] = Field(default_factory=list, description="Parent commits")
    stats: Optional[Dict[str, Any]] = Field(default=None, description="Commit statistics")
    files: Optional[List[Dict[str, Any]]] = Field(default=None, description="Changed files")
    html_url: str = Field(..., description="Commit URL")

class GitHubWorkflow(BaseModel):
    """GitHub workflow entity schema."""
    id: int = Field(..., description="Workflow ID")
    name: str = Field(..., description="Workflow name")
    path: str = Field(..., description="Workflow file path")
    state: str = Field(..., description="Workflow state")
    created_at: datetime = Field(..., description="Workflow creation date")
    updated_at: datetime = Field(..., description="Last update date")
    html_url: str = Field(..., description="Workflow URL")
    badge_url: str = Field(..., description="Workflow badge URL")

# Search and Response Schemas

class GitHubSearchQuery(BaseModel):
    """GitHub search query schema."""
    query: str = Field(..., description="Search query string")
    entity_types: Optional[List[str]] = Field(default=None, description="Entity types to search")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Additional filters")
    sort: Optional[str] = Field(default=None, description="Sort field")
    order: Optional[str] = Field(default="desc", description="Sort order")
    per_page: int = Field(default=30, description="Results per page")
    page: int = Field(default=1, description="Page number")
    
    @validator('per_page')
    def validate_per_page(cls, v):
        if v < 1 or v > 100:
            raise ValueError('per_page must be between 1 and 100')
        return v
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('page must be >= 1')
        return v

class GitHubSyncRequest(BaseModel):
    """GitHub sync request schema."""
    repository_ids: Optional[List[int]] = Field(default=None, description="Specific repository IDs to sync")
    organization_ids: Optional[List[int]] = Field(default=None, description="Specific organization IDs to sync")
    entity_types: Optional[List[str]] = Field(default=None, description="Entity types to sync")
    incremental: bool = Field(default=True, description="Perform incremental sync")
    since: Optional[datetime] = Field(default=None, description="Sync changes since this date")
    force_full_sync: bool = Field(default=False, description="Force full synchronization")

class GitHubConnectorResponse(BaseModel):
    """GitHub connector response schema."""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Response data")
    errors: List[str] = Field(default_factory=list, description="Error messages")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")