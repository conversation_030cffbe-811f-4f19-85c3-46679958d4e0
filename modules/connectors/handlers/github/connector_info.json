{"source_type": "github", "name": "GitHub", "connector_type": "structured", "category": "Code Repository", "icon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTgiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5OCA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00OC44NTQgMEM3NS42NjEgMCA5Ny43MDcgMjIuMDQ2IDk3LjcwNyA0OC44NTNDOTcuNzA3IDcwLjQ0IDg1LjY5IDg5LjA1MyA2OC4yNjQgOTUuNTQ3QzY1Ljk5NCA5Ni4wNDcgNjUuMTI0IDk0LjcxNyA2NS4xMjQgOTMuNTY3QzY1LjEyNCA5Mi4zNjcgNjUuMTY0IDg4LjA5NyA2NS4xNjQgODQuNzI3QzY1LjE2NCA3OS4yMzcgNjIuNzQ0IDc1LjY5NyA1OS44NzQgNzMuOTI3QzY5LjI1NCA3Mi43NzcgNzkuNzU0IDY4LjUwNyA3OS43NTQgNTQuNzI3Qzc5Ljc1NCA0OS4yMzcgNzcuNzM0IDQ0Ljk2NyA3NC4zNjQgNDEuNzk3Qzc0Ljk2NCAzOS4zNzcgNzQuNzY0IDM1LjgzNyA3My4yNjQgMzMuNDE3QzcxLjc2NCAzMi4yNjcgNjguNzI0IDMzLjQxNyA2NS4xNjQgMzYuNTg3QzYwLjY3NCAzNS40MzcgNTQuODc0IDM1LjQzNyA0OS4zODQgMzYuNTg3QzQ1LjgyNCAzMy40MTcgNDIuNzg0IDMyLjI2NyA0MS4yODQgMzMuNDE3QzM5Ljc4NCAzNS44MzcgMzkuNTg0IDM5LjM3NyA0MC4xODQgNDEuNzk3QzM2LjgxNCA0NC45NjcgMzQuNzk0IDQ5LjIzNyAzNC43OTQgNTQuNzI3QzM0Ljc5NCA2OC41MDcgNDUuMjk0IDcyLjc3NyA1NC42NzQgNzMuOTI3QzUyLjQwNCA3NS4zOTcgNTAuNDg0IDc4LjE5NyA0OS44ODQgODIuNDY3QzQ3LjYxNCA4My42MTcgNDMuNzQ0IDgzLjYxNyA0MS4yODQgNzkuMzQ3QzM4LjgyNCA3NS4wNzcgMzQuNzk0IDc0LjkyNyAzMi41MjQgNzQuOTI3QzMwLjI1NCA3NC45MjcgMzAuMjU0IDc2LjA3NyAzMi41MjQgNzYuMDc3QzM0Ljc5NCA3Ni4wNzcgMzYuODE0IDc4LjE5NyAzOC44MjQgODEuMzY3QzQwLjgzNCA4NC41MzcgNDUuMjk0IDg2LjY1NyA1MC43ODQgODYuNjU3QzUwLjc4NCA4OS44MjcgNTAuODI0IDkyLjM2NyA1MC44MjQgOTMuNTY3QzUwLjgyNCA5NC43MTcgNDkuOTU0IDk2LjA0NyA0Ny42ODQgOTUuNTQ3QzMwLjI1NCA4OS4wNTMgMTguMjM3IDcwLjQ0IDE4LjIzNyA0OC44NTNDMTguMjM3IDIyLjA0NiA0MC4yODMgMCA2Ny4wOSAwSDQ4Ljg1NFoiIGZpbGw9IiMxQjFGMjMiLz4KPC9zdmc+", "description": "GitHub is a web-based platform for version control and collaboration using Git. It provides hosting for software development and offers distributed version control, source code management, bug tracking, feature requests, task management, continuous integration, and wikis for every project.", "purpose": "This connector integrates with GitHub to fetch comprehensive structured data about repositories, issues, pull requests, commits, users, organizations, workflows, and more. It enables advanced querying and analysis of development workflows, code collaboration patterns, and project management activities by storing the data in a knowledge graph with rich relationships.", "nodes": [], "relationships": [], "example_usage": "Useful for analyzing software development productivity, code review patterns, contributor activity, repository insights, issue tracking trends, and collaboration networks. Enables searching across all GitHub entities in an organization using semantic, graph-based, or traditional search methods.", "example_queries": ["Find all repositories with Python code owned by our organization", "List open issues assigned to john.doe in the last sprint", "Show pull requests merged in the last week with high complexity", "Which developer has the most commits across all repositories?", "Find repositories that haven't been updated in 6 months", "Show all issues labeled as 'bug' with high priority", "List pull requests that are waiting for review for more than 3 days", "Find all workflows that failed in the last 24 hours", "Which repositories have the most stars and forks?", "Show commit activity trends for the past month", "Find all security vulnerabilities reported in our repositories", "List contributors who joined the organization in the last quarter", "Show repositories with the most active discussions", "Find all releases published in the current year", "Which teams have access to the most critical repositories?", "Show code review patterns and approval times", "Find all GitHub Actions workflows using deprecated actions", "List repositories with missing or outdated documentation", "Show dependency relationships between repositories", "Find all issues that reference external systems or tickets"], "supported_features": ["Repository management and analytics", "Issue tracking and management", "Pull request workflow analysis", "Commit history and code analysis", "User and organization management", "GitHub Actions workflow monitoring", "Code search and discovery", "Security and vulnerability tracking", "Project and milestone management", "Team collaboration insights", "Release and deployment tracking", "Webhook integration for real-time updates", "Advanced search across all entity types", "Graph-based relationship analysis", "Semantic search capabilities"], "api_endpoints": {"rest_api": "https://api.github.com", "graphql_api": "https://api.github.com/graphql", "webhooks": "https://docs.github.com/en/developers/webhooks-and-events/webhooks"}, "authentication": {"type": "personal_access_token", "scopes_required": ["repo", "read:org", "read:user", "read:project", "workflow"], "documentation": "https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token"}, "rate_limits": {"rest_api": "5000 requests per hour", "graphql_api": "5000 points per hour", "search_api": "30 requests per minute"}, "data_freshness": {"real_time": "Webhook-based updates for immediate changes", "batch_sync": "Configurable full synchronization intervals", "incremental": "Delta updates based on last modified timestamps"}, "compliance": {"data_privacy": "Respects GitHub's privacy settings and access controls", "security": "Uses secure token-based authentication", "audit": "Maintains audit logs of all data access and modifications"}}