# GitHub Connector Service

A comprehensive GitHub integration service that provides enhanced connectivity, data fetching, knowledge graph storage, and intelligent search capabilities.

## Overview

The GitHub Connector Service implements four key enhanced methods:
- **`connect()`**: Establishes connections to GitHub API and Neo4j database
- **`fetch_data()`**: Retrieves GitHub data with configurable limits and relationship mapping
- **`store_context()`**: Stores fetched data as a knowledge graph in Neo4j
- **`search()`**: Intelligent knowledge graph search with multiple search strategies

## Features

### Enhanced Connection Management
- Uses GitHub Personal Access Token from environment variables
- Establishes both GitHub API and Neo4j database connections
- Comprehensive error handling and connection validation

### Intelligent Data Fetching
- **Minimal Data Limits**: Configurable limits to prevent overwhelming data retrieval
  - 2 repositories maximum
  - 5 issues per repository
  - 3 users maximum
  - 2 organizations maximum
- **Relationship Mapping**: Automatically creates relationships between entities during fetch
- **Rate Limit Awareness**: Respects GitHub API rate limits

### Knowledge Graph Storage
- **Neo4j Integration**: Complete Neo4j handler for knowledge graph operations
- **Entity Types**: Supports GitHubRepository, GitHubUser, GitHubOrganization, GitHubIssue nodes
- **Relationship Types**: OWNS_REPOSITORY, HAS_ISSUE, CREATED_BY, ASSIGNED_TO, etc.
- **Dynamic Queries**: Uses parameterized Cypher queries for secure operations

### Intelligent Search System
- **Multiple Search Strategies**: Entity, relationship, graph traversal, and hybrid search
- **Query Analysis**: Automatic detection of entity types and relationships in natural language
- **Low Latency**: Optimized Neo4j queries with sub-second response times
- **Fallback Support**: GitHub API fallback when knowledge graph is unavailable
- **Flexible Queries**: Supports complex natural language queries with filters

## Setup

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# GitHub API Configuration
GITHUB_TOKEN=your_github_personal_access_token_here

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password
```

### Dependencies

The connector requires the following Python packages:
- `github` (PyGithub)
- `neo4j`
- `python-dotenv`
- `requests`

Install dependencies:
```bash
pip install PyGithub neo4j python-dotenv requests
```

## Usage

### Basic Usage

```python
from modules.connectors.handlers.github.service import GitHubConnectorService

# Initialize the service
github_service = GitHubConnectorService()

# Connect to GitHub API and Neo4j
connection_result = github_service.connect()
print(f"Connection status: {connection_result['status']}")

# Fetch data with minimal limits
fetch_result = github_service.fetch_data()
print(f"Repositories fetched: {len(fetch_result['data']['repositories'])}")

# Store context in Neo4j knowledge graph
store_result = github_service.store_context(fetch_result['data'])
print(f"Nodes created: {store_result['nodes_created']}")

# Search the knowledge graph
search_result = github_service.search("python repositories", limit=10)
print(f"Search results: {search_result.total_count}")
```

### Advanced Search Usage

```python
from modules.connectors.handlers.github.service import GitHubConnectorService

github_service = GitHubConnectorService()
github_service.connect()

# Entity-focused search
entity_results = github_service.search(
    query="machine learning repositories",
    limit=5,
    search_type="entity"
)

# Relationship-focused search
relationship_results = github_service.search(
    query="repositories owned by microsoft",
    limit=10,
    search_type="relationship"
)

# Graph traversal search
traversal_results = github_service.search(
    query="connection between react and facebook",
    limit=5,
    search_type="traversal"
)

# Hybrid search (default)
hybrid_results = github_service.search(
    query="open source javascript libraries with high stars",
    limit=15,
    search_type="hybrid"
)

# Process results
for result in hybrid_results.results:
    print(f"Title: {result.title}")
    print(f"Type: {result.entity_type}")
    print(f"Summary: {result.summary}")
    print(f"URL: {result.url}")
    print(f"Tags: {', '.join(result.tags)}")
    print("-" * 40)
```

### Search Query Examples

The search system supports natural language queries:

```python
# Simple entity searches
github_service.search("python repositories")
github_service.search("user octocat")
github_service.search("issues with bug label")

# Relationship searches
github_service.search("repositories owned by google")
github_service.search("issues created by torvalds")
github_service.search("users who are members of microsoft")

# Complex queries with filters
github_service.search("language:python machine learning")
github_service.search("user:facebook state:open issues")
github_service.search("repo:microsoft/vscode javascript files")

# Graph traversal queries
github_service.search("path between react and vue")
github_service.search("connection from user to organization")
github_service.search("related projects to tensorflow")
```

### Testing

Run the connector test script to verify the basic implementation:

```bash
python test_github_connector.py
```

Run the search test script to verify search functionality:

```bash
python test_github_search.py
```

The connector test script will:
1. Verify environment variable configuration
2. Test GitHub API connection
3. Test Neo4j database connection
4. Fetch minimal data from GitHub
5. Store data in Neo4j knowledge graph
6. Report success/failure status

The search test script will:
1. Test various search query types (entity, relationship, traversal, hybrid)
2. Validate search performance and latency
3. Test edge cases and error handling
4. Verify result quality and relevance

## Architecture

### Core Components

1. **GitHubConnectorService** (`service.py`)
   - Main service class inheriting from BaseConnector
   - Implements enhanced connect(), fetch_data(), and store_context() methods

2. **GitHubConnection** (`connection.py`)
   - Handles GitHub API authentication using environment variables
   - Provides connection validation and error handling

3. **GitHubNeo4jHandler** (`neo4j_handler.py`)
   - Manages Neo4j database operations
   - Creates nodes and relationships for GitHub entities
   - Provides context manager support

4. **Constants** (`constants/`)
   - **entities.py**: Defines GitHub entity types and structures
   - **relationships.py**: Defines relationship types between entities

### Data Flow

```
GitHub API → fetch_data() → Data Processing → store_context() → Neo4j Knowledge Graph
     ↑                                                              ↓
Environment Variables                                        Nodes & Relationships
                                                                     ↓
                                                            search() → Search Results
```

### Search Architecture

```
Natural Language Query → Query Analysis → Search Strategy Selection
                              ↓
                    ┌─────────────────────────────┐
                    │     Search Strategies       │
                    ├─────────────────────────────┤
                    │ • Entity Search             │
                    │ • Relationship Search       │
                    │ • Graph Traversal          │
                    │ • Hybrid Search            │
                    └─────────────────────────────┘
                              ↓
                    ┌─────────────────────────────┐
                    │   Neo4j Knowledge Graph     │
                    │                             │
                    │ Cypher Queries → Results    │
                    └─────────────────────────────┘
                              ↓
                    ┌─────────────────────────────┐
                    │    Fallback (if needed)     │
                    │                             │
                    │ GitHub API → Results        │
                    └─────────────────────────────┘
                              ↓
                    Result Processing & Ranking → Final Results
```

#### Search Strategy Selection

1. **Entity Search**: Triggered by entity-focused keywords (repo, user, issue, org)
2. **Relationship Search**: Triggered by relationship keywords (owned by, created by, member of)
3. **Graph Traversal**: Triggered by connection keywords (path between, related to, connected)
4. **Hybrid Search**: Default strategy combining multiple approaches

### Entity Relationships

The knowledge graph creates the following relationships:

- **User → Repository**: `OWNS_REPOSITORY`
- **Organization → Repository**: `OWNS_REPOSITORY`
- **Repository → Issue**: `HAS_ISSUE`
- **User → Issue**: `CREATED_BY`, `ASSIGNED_TO`
- **User → User**: `FOLLOWS`
- **User → Organization**: `MEMBER_OF`

## Configuration

### Data Limits

The service uses minimal data limits to prevent overwhelming API calls:

```python
DATA_LIMITS = {
    'repositories': 2,
    'issues_per_repo': 5,
    'users': 3,
    'organizations': 2,
    'comments_per_issue': 3,
    'commits_per_repo': 5
}
```

These limits can be adjusted in the service configuration as needed.

### Neo4j Schema

The knowledge graph uses the following node labels:
- `GitHubRepository`
- `GitHubUser`
- `GitHubOrganization`
- `GitHubIssue`

Each node contains relevant properties from the GitHub API response.

## Error Handling

The service provides comprehensive error handling:

- **Connection Errors**: GitHub API and Neo4j connection failures
- **Authentication Errors**: Invalid or missing GitHub tokens
- **Rate Limit Errors**: GitHub API rate limit exceeded
- **Data Processing Errors**: Invalid or malformed API responses
- **Database Errors**: Neo4j query execution failures

All errors are logged with appropriate detail levels and returned in structured response formats.

## Security Considerations

- **Environment Variables**: Sensitive credentials stored in .env file
- **Token Security**: GitHub Personal Access Token with minimal required permissions
- **Parameterized Queries**: Neo4j queries use parameters to prevent injection attacks
- **Rate Limiting**: Respects GitHub API rate limits to prevent account suspension

## Troubleshooting

### Common Issues

1. **GitHub Token Issues**
   - Ensure `GITHUB_TOKEN` is set in .env file
   - Verify token has required permissions (repo, user, org access)
   - Check token expiration date

2. **Neo4j Connection Issues**
   - Verify Neo4j server is running
   - Check Neo4j credentials in .env file
   - Ensure Neo4j URI format is correct

3. **Rate Limit Errors**
   - Reduce data limits in configuration
   - Implement retry logic with exponential backoff
   - Use authenticated requests for higher rate limits

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When contributing to the GitHub connector:

1. Follow the existing code structure and patterns
2. Add comprehensive error handling
3. Include logging for debugging purposes
4. Update tests for new functionality
5. Document any new configuration options

## License

This GitHub Connector Service is part of the larger organization service structure and follows the same licensing terms.