"""
GitHub Connection Handler

This module handles GitHub API connections, authentication, and health checks.
Supports both REST API v4 and GraphQL API v4 endpoints.
"""

import logging
import requests
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import time

logger = logging.getLogger(__name__)

class GitHubConnection:
    """
    Handles GitHub API connections with authentication, rate limiting, and health checks.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize GitHub connection with configuration.
        
        Args:
            config: Configuration dictionary containing GitHub API settings
        """
        self.config = config
        # Use environment variable for GitHub token, fallback to config
        self.token = os.getenv('GITHUB_TOKEN') or config.get('token')
        if not self.token:
            raise ValueError("GitHub token not found in environment variables or config")
        
        self.base_url = config.get('api_base_url', 'https://api.github.com')
        self.graphql_url = config.get('graphql_url', 'https://api.github.com/graphql')
        self.timeout = config.get('timeout_seconds', 30)
        self.rate_limit_per_hour = config.get('rate_limit_per_hour', 5000)
        
        # Rate limiting tracking
        self.rate_limit_remaining = self.rate_limit_per_hour
        self.rate_limit_reset_time = None
        self.last_rate_limit_check = None
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Connector/1.0'
        })
    
    def connect(self) -> bool:
        """
        Establish connection to GitHub API and validate credentials.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info("Establishing connection to GitHub API...")
            
            # Test connection with user endpoint
            response = self.session.get(
                f"{self.base_url}/user",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"Successfully connected to GitHub API as user: {user_data.get('login', 'Unknown')}")
                
                # Update rate limit information
                self._update_rate_limit_info(response.headers)
                
                return True
            elif response.status_code == 401:
                logger.error("GitHub API authentication failed. Please check your token.")
                return False
            else:
                logger.error(f"GitHub API connection failed with status: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to connect to GitHub API: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during GitHub connection: {str(e)}")
            return False
    
    def health_check(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Perform comprehensive health check of GitHub API connection.
        
        Returns:
            Tuple[bool, Dict]: (is_healthy, health_info)
        """
        health_info = {
            'timestamp': datetime.now().isoformat(),
            'api_status': 'unknown',
            'rate_limit_remaining': 0,
            'rate_limit_reset': None,
            'authenticated_user': None,
            'permissions': [],
            'errors': []
        }
        
        try:
            # Check API status
            status_response = self.session.get(
                "https://www.githubstatus.com/api/v2/status.json",
                timeout=10
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                health_info['api_status'] = status_data.get('status', {}).get('indicator', 'unknown')
            
            # Check authentication and permissions
            user_response = self.session.get(
                f"{self.base_url}/user",
                timeout=self.timeout
            )
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                health_info['authenticated_user'] = user_data.get('login')
                
                # Update rate limit info
                self._update_rate_limit_info(user_response.headers)
                health_info['rate_limit_remaining'] = self.rate_limit_remaining
                health_info['rate_limit_reset'] = self.rate_limit_reset_time.isoformat() if self.rate_limit_reset_time else None
                
                # Check token permissions (scopes)
                scopes = user_response.headers.get('X-OAuth-Scopes', '').split(', ')
                health_info['permissions'] = [scope.strip() for scope in scopes if scope.strip()]
                
                return True, health_info
            else:
                health_info['errors'].append(f"Authentication failed: {user_response.status_code}")
                return False, health_info
                
        except Exception as e:
            health_info['errors'].append(f"Health check failed: {str(e)}")
            return False, health_info
    
    def make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make authenticated request to GitHub API with rate limiting.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response: API response
        """
        # Check rate limit before making request
        self._check_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(
                method=method.upper(),
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # Update rate limit information
            self._update_rate_limit_info(response.headers)
            
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"GitHub API request failed: {str(e)}")
            raise
    
    def make_graphql_request(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make GraphQL request to GitHub API.
        
        Args:
            query: GraphQL query string
            variables: Optional variables for the query
            
        Returns:
            Dict: GraphQL response data
        """
        # Check rate limit before making request
        self._check_rate_limit()
        
        payload = {'query': query}
        if variables:
            payload['variables'] = variables
        
        try:
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=self.timeout
            )
            
            # Update rate limit information
            self._update_rate_limit_info(response.headers)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"GitHub GraphQL request failed: {str(e)}")
            raise
    
    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """
        Update rate limit information from response headers.
        
        Args:
            headers: Response headers from GitHub API
        """
        try:
            self.rate_limit_remaining = int(headers.get('X-RateLimit-Remaining', self.rate_limit_remaining))
            
            reset_timestamp = headers.get('X-RateLimit-Reset')
            if reset_timestamp:
                self.rate_limit_reset_time = datetime.fromtimestamp(int(reset_timestamp))
            
            self.last_rate_limit_check = datetime.now()
            
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to parse rate limit headers: {str(e)}")
    
    def _check_rate_limit(self):
        """
        Check rate limit and wait if necessary.
        """
        if self.rate_limit_remaining <= 10:  # Conservative threshold
            if self.rate_limit_reset_time and datetime.now() < self.rate_limit_reset_time:
                wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
                if wait_time > 0:
                    logger.warning(f"Rate limit nearly exceeded. Waiting {wait_time:.1f} seconds...")
                    time.sleep(min(wait_time, 300))  # Max wait 5 minutes
    
    def close(self):
        """
        Close the connection session.
        """
        if self.session:
            self.session.close()
            logger.info("GitHub API connection closed")
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()