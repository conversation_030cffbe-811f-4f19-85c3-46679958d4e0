"""
GitHub Knowledge Graph Search Implementation

This module contains efficient search functionality for the GitHub connector
using Neo4j knowledge graph storage for optimal retrieval performance.
"""

import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from modules.connectors.utilities.constant.schemas import SearchResultItem
from .neo4j_handler import GitHubNeo4jHandler
from .constants.entities import EntityType
from .constants.relationships import GitHubRelationshipType

logger = logging.getLogger(__name__)

class SearchType(Enum):
    """Types of search operations"""
    ENTITY_SEARCH = "entity"
    RELATIONSHIP_SEARCH = "relationship"
    SEMANTIC_SEARCH = "semantic"
    GRAPH_TRAVERSAL = "traversal"
    HYBRID_SEARCH = "hybrid"

@dataclass
class SearchContext:
    """Search context for query optimization"""
    query_text: str
    entity_types: List[str] = None
    relationship_types: List[str] = None
    filters: Dict[str, Any] = None
    limit: int = 10
    search_type: SearchType = SearchType.HYBRID_SEARCH
    include_relationships: bool = True

class GitHubKnowledgeGraphSearch:
    """Efficient GitHub Knowledge Graph Search Implementation"""
    
    def __init__(self, neo4j_handler: GitHubNeo4jHandler):
        self.neo4j_handler = neo4j_handler
        self.entity_patterns = self._build_entity_patterns()
        self.relationship_patterns = self._build_relationship_patterns()
    
    def _build_entity_patterns(self) -> Dict[str, List[str]]:
        """Build regex patterns for entity detection in queries"""
        return {
            'GitHubRepository': [
                r'\b(?:repo|repository|repositories)\b',
                r'\b(?:project|codebase)\b',
                r'\.git\b',
                r'\bgithub\.com/[\w-]+/[\w-]+\b'
            ],
            'GitHubUser': [
                r'\b(?:user|users|developer|author|contributor)\b',
                r'\b(?:@[\w-]+)\b',
                r'\b(?:profile|account)\b'
            ],
            'GitHubOrganization': [
                r'\b(?:org|organization|company|team)\b',
                r'\b(?:enterprise|business)\b'
            ],
            'GitHubIssue': [
                r'\b(?:issue|issues|bug|feature|task)\b',
                r'\b(?:#\d+)\b',
                r'\b(?:ticket|problem)\b'
            ]
        }
    
    def _build_relationship_patterns(self) -> Dict[str, List[str]]:
        """Build regex patterns for relationship detection in queries"""
        return {
            'OWNS_REPOSITORY': [
                r'\b(?:owns|owned by|belongs to)\b',
                r'\b(?:created by|author of)\b'
            ],
            'HAS_ISSUE': [
                r'\b(?:has issue|contains issue|issue in)\b',
                r'\b(?:reported in|found in)\b'
            ],
            'CREATED_BY': [
                r'\b(?:created by|authored by|made by)\b',
                r'\b(?:from|by)\b'
            ],
            'ASSIGNED_TO': [
                r'\b(?:assigned to|given to)\b',
                r'\b(?:responsible|assignee)\b'
            ],
            'MEMBER_OF': [
                r'\b(?:member of|part of|in)\b',
                r'\b(?:works at|employed by)\b'
            ]
        }
    
    def analyze_query(self, query_text: str) -> SearchContext:
        """
        Analyze query to determine optimal search strategy
        
        Args:
            query_text: Natural language query
            
        Returns:
            SearchContext: Optimized search context
        """
        query_lower = query_text.lower()
        
        # Detect entity types
        detected_entities = []
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    detected_entities.append(entity_type)
                    break
        
        # Detect relationship types
        detected_relationships = []
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    detected_relationships.append(rel_type)
                    break
        
        # Extract filters
        filters = self._extract_filters(query_text)
        
        # Determine search type
        search_type = self._determine_search_type(
            query_text, detected_entities, detected_relationships
        )
        
        return SearchContext(
            query_text=query_text,
            entity_types=detected_entities or None,
            relationship_types=detected_relationships or None,
            filters=filters,
            search_type=search_type,
            include_relationships=bool(detected_relationships)
        )
    
    def _extract_filters(self, query_text: str) -> Dict[str, Any]:
        """Extract filters from query text"""
        filters = {}
        
        # Language filter
        lang_match = re.search(r'\b(?:language|lang):\s*(\w+)', query_text, re.IGNORECASE)
        if lang_match:
            filters['language'] = lang_match.group(1)
        
        # User filter
        user_match = re.search(r'\b(?:user|author):\s*([\w-]+)', query_text, re.IGNORECASE)
        if user_match:
            filters['user'] = user_match.group(1)
        
        # Repository filter
        repo_match = re.search(r'\b(?:repo|repository):\s*([\w-]+/[\w-]+)', query_text, re.IGNORECASE)
        if repo_match:
            filters['repository'] = repo_match.group(1)
        
        # State filter
        state_match = re.search(r'\b(?:state|status):\s*(\w+)', query_text, re.IGNORECASE)
        if state_match:
            filters['state'] = state_match.group(1)
        
        return filters
    
    def _determine_search_type(self, query_text: str, entities: List[str], relationships: List[str]) -> SearchType:
        """Determine optimal search type based on query analysis"""
        query_lower = query_text.lower()
        
        # Check for traversal keywords
        traversal_keywords = ['connected to', 'related to', 'path between', 'relationship']
        if any(keyword in query_lower for keyword in traversal_keywords):
            return SearchType.GRAPH_TRAVERSAL
        
        # Check for relationship-focused queries
        if relationships and len(relationships) > len(entities):
            return SearchType.RELATIONSHIP_SEARCH
        
        # Check for specific entity queries
        if entities and not relationships:
            return SearchType.ENTITY_SEARCH
        
        # Default to hybrid search
        return SearchType.HYBRID_SEARCH
    
    def search_entities(self, context: SearchContext) -> List[SearchResultItem]:
        """
        Search for entities in the knowledge graph
        
        Args:
            context: Search context
            
        Returns:
            List[SearchResultItem]: Search results
        """
        results = []
        
        try:
            # Build Cypher query for entity search
            cypher_parts = []
            params = {}
            
            if context.entity_types:
                for i, entity_type in enumerate(context.entity_types):
                    label = entity_type
                    var_name = f"n{i}"
                    
                    # Build WHERE clause for text search
                    where_conditions = []
                    
                    # Text search in appropriate properties based on entity type
                    if context.query_text:
                        text_conditions = []

                        # Define searchable properties for each entity type
                        if entity_type == 'GitHubRepository':
                            searchable_props = ['name', 'description', 'full_name']
                        elif entity_type == 'GitHubUser':
                            searchable_props = ['login', 'name']
                        elif entity_type == 'GitHubOrganization':
                            searchable_props = ['login', 'name', 'description']
                        elif entity_type == 'GitHubIssue':
                            searchable_props = ['title', 'body']
                        elif entity_type == 'GitHubPullRequest':
                            searchable_props = ['title', 'body']
                        else:
                            # Default fallback
                            searchable_props = ['name']

                        for prop in searchable_props:
                            text_conditions.append(f"toLower({var_name}.{prop}) CONTAINS toLower($query_text)")

                        if text_conditions:
                            where_conditions.append(f"({' OR '.join(text_conditions)})")
                            params['query_text'] = context.query_text
                    
                    # Apply filters
                    if context.filters:
                        for key, value in context.filters.items():
                            if key in ['language', 'state', 'user']:
                                where_conditions.append(f"{var_name}.{key} = ${key}")
                                params[key] = value
                    
                    where_clause = " AND ".join(where_conditions) if where_conditions else "true"
                    
                    cypher_parts.append(f"""
                        MATCH ({var_name}:{label})
                        WHERE {where_clause}
                        RETURN '{entity_type}' as entity_type, {var_name} as entity
                        ORDER BY {var_name}.updated_at DESC
                        LIMIT {context.limit}
                    """)
            
            if cypher_parts:
                cypher_query = " UNION ALL ".join(cypher_parts)
                
                with self.neo4j_handler.get_session() as session:
                    result = session.run(cypher_query, params)
                    
                    for record in result:
                        entity_data = dict(record['entity'])
                        entity_type = record['entity_type']
                        
                        search_item = self._create_search_result_item(
                            entity_data, entity_type
                        )
                        if search_item:
                            results.append(search_item)
        
        except Exception as e:
            logger.error(f"Error in entity search: {str(e)}")
        
        return results[:context.limit]
    
    def search_relationships(self, context: SearchContext) -> List[SearchResultItem]:
        """
        Search for relationships in the knowledge graph
        
        Args:
            context: Search context
            
        Returns:
            List[SearchResultItem]: Search results with relationship context
        """
        results = []
        
        try:
            if not context.relationship_types:
                return results
            
            cypher_parts = []
            params = {'query_text': context.query_text}
            
            for rel_type in context.relationship_types:
                cypher_parts.append(f"""
                    MATCH (a)-[r:{rel_type}]->(b)
                    WHERE toLower(a.name) CONTAINS toLower($query_text) 
                       OR toLower(b.name) CONTAINS toLower($query_text)
                       OR toLower(a.login) CONTAINS toLower($query_text)
                       OR toLower(b.login) CONTAINS toLower($query_text)
                    RETURN a, r, b, '{rel_type}' as rel_type
                    LIMIT {context.limit}
                """)
            
            if cypher_parts:
                cypher_query = " UNION ALL ".join(cypher_parts)
                
                with self.neo4j_handler.get_session() as session:
                    result = session.run(cypher_query, params)
                    
                    for record in result:
                        source_entity = dict(record['a'])
                        target_entity = dict(record['b'])
                        rel_type = record['rel_type']
                        
                        # Create relationship-based search result
                        search_item = self._create_relationship_search_result(
                            source_entity, target_entity, rel_type
                        )
                        if search_item:
                            results.append(search_item)
        
        except Exception as e:
            logger.error(f"Error in relationship search: {str(e)}")
        
        return results[:context.limit]
    
    def graph_traversal_search(self, context: SearchContext) -> List[SearchResultItem]:
        """
        Perform graph traversal search for complex queries
        
        Args:
            context: Search context
            
        Returns:
            List[SearchResultItem]: Search results from graph traversal
        """
        results = []
        
        try:
            # Extract entities from query for traversal
            entities = self._extract_entity_names(context.query_text)
            
            if len(entities) >= 2:
                # Find paths between entities
                cypher_query = """
                    MATCH (start), (end)
                    WHERE (toLower(start.name) CONTAINS toLower($entity1) OR toLower(start.login) CONTAINS toLower($entity1))
                      AND (toLower(end.name) CONTAINS toLower($entity2) OR toLower(end.login) CONTAINS toLower($entity2))
                    MATCH path = shortestPath((start)-[*1..3]-(end))
                    RETURN path, start, end
                    LIMIT $limit
                """
                
                params = {
                    'entity1': entities[0],
                    'entity2': entities[1],
                    'limit': context.limit
                }
                
                with self.neo4j_handler.get_session() as session:
                    result = session.run(cypher_query, params)
                    
                    for record in result:
                        path_result = self._create_path_search_result(
                            record['path'], record['start'], record['end']
                        )
                        if path_result:
                            results.append(path_result)
        
        except Exception as e:
            logger.error(f"Error in graph traversal search: {str(e)}")
        
        return results
    
    def hybrid_search(self, context: SearchContext) -> List[SearchResultItem]:
        """
        Perform hybrid search combining multiple search strategies
        
        Args:
            context: Search context
            
        Returns:
            List[SearchResultItem]: Combined search results
        """
        all_results = []
        
        try:
            # Entity search
            entity_results = self.search_entities(context)
            all_results.extend(entity_results)
            
            # Relationship search if relationships detected
            if context.relationship_types:
                rel_results = self.search_relationships(context)
                all_results.extend(rel_results)
            
            # Graph traversal for complex queries
            if context.search_type == SearchType.GRAPH_TRAVERSAL:
                traversal_results = self.graph_traversal_search(context)
                all_results.extend(traversal_results)
            
            # Remove duplicates and sort by relevance
            unique_results = self._deduplicate_results(all_results)
            sorted_results = self._sort_by_relevance(unique_results, context.query_text)
            
            return sorted_results[:context.limit]
        
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            return []
    
    def _extract_entity_names(self, query_text: str) -> List[str]:
        """Extract potential entity names from query text"""
        # Simple extraction - could be enhanced with NLP
        words = query_text.split()
        entities = []
        
        for word in words:
            # Remove common words and punctuation
            clean_word = re.sub(r'[^\w-]', '', word)
            if len(clean_word) > 2 and clean_word.lower() not in ['the', 'and', 'or', 'in', 'on', 'at', 'to', 'for']:
                entities.append(clean_word)
        
        return entities[:5]  # Limit to first 5 potential entities
    
    def _create_search_result_item(self, entity_data: Dict[str, Any], entity_type: str) -> Optional[SearchResultItem]:
        """Create SearchResultItem from entity data"""
        try:
            # Determine title based on entity type
            title = (entity_data.get('name') or 
                    entity_data.get('login') or 
                    entity_data.get('full_name') or 
                    entity_data.get('title') or 
                    f"{entity_type} #{entity_data.get('id', 'unknown')}")
            
            # Determine content
            content = (entity_data.get('description') or 
                      entity_data.get('body') or 
                      entity_data.get('bio') or 
                      '')
            
            # Create URL
            url = entity_data.get('html_url', '')
            
            # Create summary
            summary = f"{entity_type}: {title}"
            if content:
                summary += f" - {content[:100]}..."
            
            # Extract metadata
            metadata = {k: v for k, v in entity_data.items() 
                       if k not in ['name', 'login', 'full_name', 'title', 'description', 'body', 'bio']}
            
            # Create tags
            tags = [entity_type.replace('GitHub', '').lower()]
            if entity_data.get('language'):
                tags.append(entity_data['language'])
            if entity_data.get('state'):
                tags.append(entity_data['state'])
            
            return SearchResultItem(
                id=f"{entity_type.lower()}:{entity_data.get('id', 'unknown')}",
                title=title,
                content=content,
                source_type="github",
                entity_type=entity_type,
                url=url,
                summary=summary,
                metadata=metadata,
                created_at=self._parse_datetime(entity_data.get('created_at')),
                updated_at=self._parse_datetime(entity_data.get('updated_at')),
                tags=tags
            )
        
        except Exception as e:
            logger.error(f"Error creating search result item: {str(e)}")
            return None
    
    def _create_relationship_search_result(self, source: Dict, target: Dict, rel_type: str) -> Optional[SearchResultItem]:
        """Create SearchResultItem for relationship results"""
        try:
            source_name = source.get('name') or source.get('login') or 'Unknown'
            target_name = target.get('name') or target.get('login') or 'Unknown'
            
            title = f"{source_name} {rel_type.replace('_', ' ').lower()} {target_name}"
            content = f"Relationship: {source_name} -> {rel_type} -> {target_name}"
            summary = f"Relationship found: {title}"
            
            return SearchResultItem(
                id=f"relationship:{source.get('id', 'unknown')}:{target.get('id', 'unknown')}",
                title=title,
                content=content,
                source_type="github",
                entity_type="GitHubRelationship",
                url="",
                summary=summary,
                metadata={
                    'source': source,
                    'target': target,
                    'relationship_type': rel_type
                },
                tags=['relationship', rel_type.lower()]
            )
        
        except Exception as e:
            logger.error(f"Error creating relationship search result: {str(e)}")
            return None
    
    def _create_path_search_result(self, path, start_node, end_node) -> Optional[SearchResultItem]:
        """Create SearchResultItem for graph path results"""
        try:
            start_name = start_node.get('name') or start_node.get('login') or 'Unknown'
            end_name = end_node.get('name') or end_node.get('login') or 'Unknown'
            
            title = f"Path from {start_name} to {end_name}"
            content = f"Graph path connecting {start_name} and {end_name}"
            summary = f"Connection found between {start_name} and {end_name}"
            
            return SearchResultItem(
                id=f"path:{start_node.get('id', 'unknown')}:{end_node.get('id', 'unknown')}",
                title=title,
                content=content,
                source_type="github",
                entity_type="GitHubPath",
                url="",
                summary=summary,
                metadata={
                    'start_node': dict(start_node),
                    'end_node': dict(end_node),
                    'path_length': len(path.relationships) if hasattr(path, 'relationships') else 0
                },
                tags=['path', 'connection']
            )
        
        except Exception as e:
            logger.error(f"Error creating path search result: {str(e)}")
            return None
    
    def _deduplicate_results(self, results: List[SearchResultItem]) -> List[SearchResultItem]:
        """Remove duplicate results based on ID"""
        seen_ids = set()
        unique_results = []
        
        for result in results:
            if result.id not in seen_ids:
                seen_ids.add(result.id)
                unique_results.append(result)
        
        return unique_results
    
    def _sort_by_relevance(self, results: List[SearchResultItem], query_text: str) -> List[SearchResultItem]:
        """Sort results by relevance to query"""
        query_lower = query_text.lower()
        
        def relevance_score(item: SearchResultItem) -> float:
            score = 0.0
            
            # Title match
            if query_lower in item.title.lower():
                score += 10.0
            
            # Content match
            if query_lower in item.content.lower():
                score += 5.0
            
            # Tag match
            for tag in item.tags:
                if query_lower in tag.lower():
                    score += 3.0
            
            # Recency bonus
            if item.updated_at:
                days_old = (datetime.now() - item.updated_at.replace(tzinfo=None)).days
                score += max(0, 5.0 - (days_old / 30))  # Bonus for recent items
            
            return score
        
        return sorted(results, key=relevance_score, reverse=True)
    
    def _parse_datetime(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string safely"""
        if not date_str:
            return None
        
        try:
            # Handle ISO format with Z
            if date_str.endswith('Z'):
                date_str = date_str.replace('Z', '+00:00')
            return datetime.fromisoformat(date_str)
        except (ValueError, TypeError):
            return None

# Legacy search implementation for GitHub API fallback
class GitHubAPISearch:
    """Fallback GitHub API search implementation"""
    
    def __init__(self, connection):
        self.connection = connection
    
    def search_repositories_api(self, query: str, limit: int = 10) -> List[SearchResultItem]:
        """Search repositories using GitHub API"""
        results = []
        
        try:
            response = self.connection.make_request(
                'GET',
                f'/search/repositories?q={query}&sort=updated&order=desc&per_page={limit}'
            )
            
            if response.status_code == 200:
                data = response.json()
                for repo in data.get('items', []):
                    results.append(SearchResultItem(
                        id=f"repository:{repo['id']}",
                        title=repo['full_name'],
                        content=repo.get('description', ''),
                        source_type="github",
                        entity_type="GitHubRepository",
                        url=repo['html_url'],
                        summary=f"Repository: {repo['full_name']}",
                        metadata={
                            'language': repo.get('language'),
                            'stars': repo['stargazers_count'],
                            'forks': repo['forks_count']
                        },
                        created_at=self._parse_datetime(repo['created_at']),
                        updated_at=self._parse_datetime(repo['updated_at']),
                        tags=[repo.get('language', 'unknown')]
                    ))
        
        except Exception as e:
            logger.error(f"Error in API repository search: {str(e)}")
        
        return results
    
    def _parse_datetime(self, date_str: str) -> Optional[datetime]:
        """Parse datetime string safely"""
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            return None