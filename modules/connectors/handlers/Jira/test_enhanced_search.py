"""
Comprehensive test suite for enhanced Jira search functionality
Tests security, performance, caching, and query processing capabilities.
"""

import unittest
import time
import logging
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Import the enhanced Jira service and related classes
from service import (
    JiraConnectorService, SearchConfig, QueryType, ParsedQuery,
    QuerySanitizer, AdvancedQueryParser, SecureCypherBuilder, SearchCache
)
from config import JiraConnectorConfig, JiraConnectionConfig, SecurityConfig, PerformanceConfig
from performance import PerformanceMonitor


class TestQuerySanitizer(unittest.TestCase):
    """Test query sanitization and security features"""
    
    def setUp(self):
        self.sanitizer = QuerySanitizer()
    
    def test_dangerous_patterns_detection(self):
        """Test detection of dangerous SQL injection patterns"""
        dangerous_queries = [
            "'; DROP TABLE issues; --",
            "SELECT * FROM users WHERE id = 1; DELETE FROM issues;",
            "issues WHERE summary CONTAINS 'test'; UPDATE issues SET status = 'closed'",
            "<script>alert('xss')</script>",
            "javascript:alert('xss')"
        ]
        
        for query in dangerous_queries:
            with self.assertRaises(ValueError):
                self.sanitizer.sanitize_query(query)
    
    def test_safe_queries_pass(self):
        """Test that safe queries pass sanitization"""
        safe_queries = [
            "find issues with bug",
            "issues assigned to Alice",
            "show issues in project Alpha",
            "what issues are blocking ISSUE-1",
            "search for performance optimization"
        ]
        
        for query in safe_queries:
            # Should not raise exception
            sanitized = self.sanitizer.sanitize_query(query)
            self.assertIsInstance(sanitized, str)
            self.assertEqual(sanitized.strip(), query.strip())
    
    def test_query_length_validation(self):
        """Test query length validation"""
        long_query = "a" * 1001  # Exceeds default max length
        
        with self.assertRaises(ValueError):
            self.sanitizer.sanitize_query(long_query)
    
    def test_empty_query_handling(self):
        """Test handling of empty or whitespace queries"""
        empty_queries = ["", "   ", "\t\n"]
        
        for query in empty_queries:
            with self.assertRaises(ValueError):
                self.sanitizer.sanitize_query(query)


class TestAdvancedQueryParser(unittest.TestCase):
    """Test advanced query parsing capabilities"""
    
    def setUp(self):
        self.parser = AdvancedQueryParser()
        self.config = SearchConfig()
    
    def test_issue_search_parsing(self):
        """Test parsing of issue search queries"""
        queries = [
            "find issues with bug",
            "search for tickets containing login",
            "show me issues about performance"
        ]
        
        for query in queries:
            parsed = self.parser.parse_query(query, self.config)
            self.assertEqual(parsed.query_type, QueryType.ISSUE_SEARCH)
            self.assertGreater(len(parsed.keywords), 0)
    
    def test_user_assignment_parsing(self):
        """Test parsing of user assignment queries"""
        queries = [
            "issues assigned to Alice",
            "what did Bob work on",
            "show tickets by user John"
        ]
        
        for query in queries:
            parsed = self.parser.parse_query(query, self.config)
            self.assertEqual(parsed.query_type, QueryType.USER_ASSIGNMENT)
            self.assertIn('user', parsed.entity_filters)
    
    def test_project_search_parsing(self):
        """Test parsing of project search queries"""
        queries = [
            "issues in project Alpha",
            "show me tickets from PROJ1",
            "what's happening in Beta project"
        ]
        
        for query in queries:
            parsed = self.parser.parse_query(query, self.config)
            self.assertEqual(parsed.query_type, QueryType.PROJECT_SEARCH)
            self.assertIn('project', parsed.entity_filters)
    
    def test_date_filter_parsing(self):
        """Test parsing of date filters"""
        test_cases = [
            ("issues updated yesterday", -1),
            ("tickets from today", 0),
            ("issues updated on 2025-06-29", None)  # Specific date
        ]
        
        for query, expected_days_offset in test_cases:
            parsed = self.parser.parse_query(query, self.config)
            
            if expected_days_offset is not None:
                expected_date = datetime.now() + timedelta(days=expected_days_offset)
                self.assertIsNotNone(parsed.date_filter)
                self.assertEqual(parsed.date_filter.date(), expected_date.date())
            else:
                self.assertIsNotNone(parsed.date_filter)
    
    def test_limit_extraction(self):
        """Test extraction of result limits"""
        test_cases = [
            ("show me 5 issues with bug", 5),
            ("limit 20 tickets from project Alpha", 20),
            ("first 3 issues assigned to Alice", 3),
            ("issues with bug", 10)  # Default limit
        ]
        
        for query, expected_limit in test_cases:
            parsed = self.parser.parse_query(query, self.config)
            self.assertEqual(parsed.limit, expected_limit)


class TestSecureCypherBuilder(unittest.TestCase):
    """Test secure Cypher query building"""
    
    def setUp(self):
        self.builder = SecureCypherBuilder()
    
    def test_issue_search_query_building(self):
        """Test building secure issue search queries"""
        keywords = ["bug", "login"]
        date_filter = datetime.now()
        limit = 10
        
        query, params = self.builder.build_issue_search_query(keywords, date_filter, limit)
        
        # Check that query uses parameters instead of string interpolation
        self.assertIn("$keyword_0", query)
        self.assertIn("$keyword_1", query)
        self.assertIn("$limit", query)
        self.assertIn("$target_date", query)
        
        # Check parameters
        self.assertEqual(params['keyword_0'], "bug")
        self.assertEqual(params['keyword_1'], "login")
        self.assertEqual(params['limit'], limit)
        self.assertEqual(params['target_date'], date_filter.date().isoformat())
    
    def test_user_assignment_query_building(self):
        """Test building secure user assignment queries"""
        user_name = "Alice"
        limit = 15
        
        query, params = self.builder.build_user_assignment_query(user_name, None, limit)
        
        self.assertIn("$user_name", query)
        self.assertIn("$limit", query)
        self.assertEqual(params['user_name'], user_name)
        self.assertEqual(params['limit'], limit)
    
    def test_project_search_query_building(self):
        """Test building secure project search queries"""
        project_name = "Alpha"
        limit = 20
        
        query, params = self.builder.build_project_search_query(project_name, None, limit)
        
        self.assertIn("$project_name", query)
        self.assertIn("$project_id", query)
        self.assertIn("$limit", query)
        self.assertEqual(params['project_name'], project_name)
        self.assertEqual(params['project_id'], project_name.upper())
        self.assertEqual(params['limit'], limit)


class TestSearchCache(unittest.TestCase):
    """Test search caching functionality"""
    
    def setUp(self):
        self.cache = SearchCache(ttl_seconds=1)  # Short TTL for testing
    
    def test_cache_set_and_get(self):
        """Test basic cache set and get operations"""
        query = "MATCH (i:JiraIssue) RETURN i"
        params = {"limit": 10}
        results = [{"id": "ISSUE-1", "summary": "Test issue"}]
        
        # Cache miss initially
        cached_results = self.cache.get(query, params)
        self.assertIsNone(cached_results)
        
        # Set cache
        self.cache.set(query, params, results)
        
        # Cache hit
        cached_results = self.cache.get(query, params)
        self.assertEqual(cached_results, results)
    
    def test_cache_expiration(self):
        """Test cache expiration"""
        query = "MATCH (i:JiraIssue) RETURN i"
        params = {"limit": 10}
        results = [{"id": "ISSUE-1", "summary": "Test issue"}]
        
        # Set cache
        self.cache.set(query, params, results)
        
        # Should be cached
        cached_results = self.cache.get(query, params)
        self.assertEqual(cached_results, results)
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Should be expired
        cached_results = self.cache.get(query, params)
        self.assertIsNone(cached_results)
    
    def test_cache_key_generation(self):
        """Test cache key generation for different queries"""
        query1 = "MATCH (i:JiraIssue) RETURN i"
        params1 = {"limit": 10}
        
        query2 = "MATCH (u:JiraUser) RETURN u"
        params2 = {"limit": 10}
        
        # Different queries should have different cache keys
        key1 = self.cache._generate_cache_key(query1, params1)
        key2 = self.cache._generate_cache_key(query2, params2)
        self.assertNotEqual(key1, key2)
        
        # Same query and params should have same cache key
        key1_duplicate = self.cache._generate_cache_key(query1, params1)
        self.assertEqual(key1, key1_duplicate)


class TestJiraConnectorService(unittest.TestCase):
    """Test the enhanced Jira connector service"""
    
    def setUp(self):
        self.config = {
            "jira_url": "https://test.atlassian.net",
            "username": "test_user",
            "password": "test_password",
            "max_results": 50,
            "default_limit": 10,
            "enable_caching": True,
            "enable_query_logging": False  # Disable for tests
        }
        self.service = JiraConnectorService(self.config)
    
    def test_service_initialization(self):
        """Test service initialization with configuration"""
        self.assertEqual(self.service.search_config.max_results, 50)
        self.assertEqual(self.service.search_config.default_limit, 10)
        self.assertTrue(self.service.search_config.enable_caching)
        self.assertIsNotNone(self.service.search_cache)
        self.assertIsNotNone(self.service.query_parser)
        self.assertIsNotNone(self.service.cypher_builder)
    
    def test_search_functionality(self):
        """Test basic search functionality"""
        test_queries = [
            "find issues with bug",
            "issues assigned to Alice",
            "show issues in project Alpha",
            "find all users",
            "issues updated yesterday"
        ]
        
        for query in test_queries:
            results = self.service.search(query)
            self.assertIsInstance(results, list)
            # Results should be non-empty for our simulated data
            if "bug" in query or "Alice" in query or "Alpha" in query:
                self.assertGreater(len(results), 0)
    
    def test_search_with_dangerous_query(self):
        """Test that dangerous queries are rejected"""
        dangerous_queries = [
            "'; DROP TABLE issues; --",
            "<script>alert('xss')</script>",
            "SELECT * FROM users; DELETE FROM issues;"
        ]
        
        for query in dangerous_queries:
            with self.assertRaises(ValueError):
                self.service.search(query)
    
    def test_search_caching(self):
        """Test search result caching"""
        query = "find issues with bug"
        
        # First search
        start_time = time.time()
        results1 = self.service.search(query)
        time1 = time.time() - start_time
        
        # Second search (should be faster due to caching)
        start_time = time.time()
        results2 = self.service.search(query)
        time2 = time.time() - start_time
        
        # Results should be identical
        self.assertEqual(results1, results2)
        
        # Second search should be faster (though this might be flaky in tests)
        # We'll just check that both completed successfully
        self.assertGreater(len(results1), 0)
        self.assertGreater(len(results2), 0)
    
    def test_search_statistics(self):
        """Test search statistics functionality"""
        # Perform some searches
        queries = ["find issues", "show users", "list projects"]
        for query in queries:
            self.service.search(query)
        
        # Get statistics
        stats = self.service.get_search_stats()
        
        self.assertIn('cache_enabled', stats)
        self.assertIn('cache_size', stats)
        self.assertIn('config', stats)
        self.assertTrue(stats['cache_enabled'])
        self.assertIsInstance(stats['cache_size'], int)
    
    def test_cache_clearing(self):
        """Test cache clearing functionality"""
        # Perform a search to populate cache
        self.service.search("find issues with bug")
        
        # Check cache has content
        stats_before = self.service.get_search_stats()
        
        # Clear cache
        self.service.clear_cache()
        
        # Check cache is empty
        stats_after = self.service.get_search_stats()
        self.assertEqual(stats_after['cache_size'], 0)


class TestPerformanceMonitoring(unittest.TestCase):
    """Test performance monitoring functionality"""
    
    def setUp(self):
        self.monitor = PerformanceMonitor(retention_hours=1, slow_query_threshold_ms=100)
    
    def test_query_recording(self):
        """Test recording of query metrics"""
        self.monitor.record_query(
            query_id="test_1",
            query_text="MATCH (i:JiraIssue) RETURN i",
            query_type="issue_search",
            execution_time_ms=150.5,
            result_count=5,
            cache_hit=False
        )
        
        stats = self.monitor.get_stats()
        self.assertEqual(stats['total_queries'], 1)
        self.assertEqual(stats['slow_queries'], 1)  # Above 100ms threshold
        self.assertEqual(stats['cache_misses'], 1)
    
    def test_slow_query_detection(self):
        """Test slow query detection and reporting"""
        # Record a slow query
        self.monitor.record_query(
            query_id="slow_1",
            query_text="MATCH (i:JiraIssue) WHERE i.summary CONTAINS 'complex' RETURN i",
            query_type="issue_search",
            execution_time_ms=1500.0,
            result_count=10,
            cache_hit=False
        )
        
        slow_queries = self.monitor.get_slow_queries(limit=5)
        self.assertEqual(len(slow_queries), 1)
        self.assertEqual(slow_queries[0]['execution_time_ms'], 1500.0)
    
    def test_cache_hit_rate_calculation(self):
        """Test cache hit rate calculation"""
        # Record cache hit
        self.monitor.record_query("q1", "query1", "type1", 50, 5, cache_hit=True)
        
        # Record cache miss
        self.monitor.record_query("q2", "query2", "type1", 75, 3, cache_hit=False)
        
        stats = self.monitor.get_stats()
        self.assertEqual(stats['cache_hit_rate'], 50.0)  # 1 hit out of 2 queries


if __name__ == '__main__':
    # Configure logging for tests
    logging.basicConfig(level=logging.WARNING)
    
    # Run tests
    unittest.main(verbosity=2)
