"""
Performance monitoring and metrics for Jira Connector
Provides query performance tracking, caching metrics, and optimization insights.
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import wraps
import threading


logger = logging.getLogger(__name__)


@dataclass
class QueryMetrics:
    """Metrics for a single query execution"""
    query_id: str
    query_text: str
    query_type: str
    execution_time_ms: float
    result_count: int
    cache_hit: bool
    timestamp: datetime
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            'query_id': self.query_id,
            'query_text': self.query_text,
            'query_type': self.query_type,
            'execution_time_ms': self.execution_time_ms,
            'result_count': self.result_count,
            'cache_hit': self.cache_hit,
            'timestamp': self.timestamp.isoformat(),
            'error': self.error
        }


@dataclass
class PerformanceStats:
    """Aggregated performance statistics"""
    total_queries: int = 0
    total_execution_time_ms: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    errors: int = 0
    slow_queries: int = 0
    avg_execution_time_ms: float = 0.0
    cache_hit_rate: float = 0.0
    queries_per_minute: float = 0.0
    
    def update(self, metrics: QueryMetrics, slow_query_threshold_ms: float = 1000.0):
        """Update statistics with new query metrics"""
        self.total_queries += 1
        self.total_execution_time_ms += metrics.execution_time_ms
        
        if metrics.cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
        
        if metrics.error:
            self.errors += 1
        
        if metrics.execution_time_ms > slow_query_threshold_ms:
            self.slow_queries += 1
        
        # Recalculate derived metrics
        self.avg_execution_time_ms = self.total_execution_time_ms / self.total_queries
        total_cache_queries = self.cache_hits + self.cache_misses
        self.cache_hit_rate = self.cache_hits / total_cache_queries if total_cache_queries > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert statistics to dictionary"""
        return {
            'total_queries': self.total_queries,
            'total_execution_time_ms': self.total_execution_time_ms,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'errors': self.errors,
            'slow_queries': self.slow_queries,
            'avg_execution_time_ms': round(self.avg_execution_time_ms, 2),
            'cache_hit_rate': round(self.cache_hit_rate * 100, 2),
            'queries_per_minute': round(self.queries_per_minute, 2)
        }


class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self, retention_hours: int = 24, slow_query_threshold_ms: float = 1000.0):
        self.retention_hours = retention_hours
        self.slow_query_threshold_ms = slow_query_threshold_ms
        self.metrics_history: deque = deque()
        self.stats = PerformanceStats()
        self.query_types_stats: Dict[str, PerformanceStats] = defaultdict(PerformanceStats)
        self._lock = threading.Lock()
        
        logger.info(f"Performance monitor initialized with {retention_hours}h retention")
    
    def record_query(self, query_id: str, query_text: str, query_type: str, 
                    execution_time_ms: float, result_count: int, 
                    cache_hit: bool = False, error: Optional[str] = None) -> None:
        """Record query execution metrics"""
        metrics = QueryMetrics(
            query_id=query_id,
            query_text=query_text[:100] + "..." if len(query_text) > 100 else query_text,
            query_type=query_type,
            execution_time_ms=execution_time_ms,
            result_count=result_count,
            cache_hit=cache_hit,
            timestamp=datetime.now(),
            error=error
        )
        
        with self._lock:
            self.metrics_history.append(metrics)
            self.stats.update(metrics, self.slow_query_threshold_ms)
            self.query_types_stats[query_type].update(metrics, self.slow_query_threshold_ms)
            
            # Clean old metrics
            self._cleanup_old_metrics()
        
        # Log slow queries
        if execution_time_ms > self.slow_query_threshold_ms:
            logger.warning(f"Slow query detected: {execution_time_ms:.2f}ms - {query_text[:50]}...")
    
    def _cleanup_old_metrics(self) -> None:
        """Remove metrics older than retention period"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        while self.metrics_history and self.metrics_history[0].timestamp < cutoff_time:
            self.metrics_history.popleft()
    
    def get_stats(self, query_type: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics"""
        with self._lock:
            if query_type:
                stats = self.query_types_stats.get(query_type, PerformanceStats())
            else:
                stats = self.stats
            
            # Calculate queries per minute
            if self.metrics_history:
                time_span_minutes = (datetime.now() - self.metrics_history[0].timestamp).total_seconds() / 60
                stats.queries_per_minute = len(self.metrics_history) / max(time_span_minutes, 1)
            
            return stats.to_dict()
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get slowest queries"""
        with self._lock:
            slow_queries = [
                m for m in self.metrics_history 
                if m.execution_time_ms > self.slow_query_threshold_ms
            ]
            
            # Sort by execution time descending
            slow_queries.sort(key=lambda x: x.execution_time_ms, reverse=True)
            
            return [q.to_dict() for q in slow_queries[:limit]]
    
    def get_query_type_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """Get performance breakdown by query type"""
        with self._lock:
            return {
                query_type: stats.to_dict() 
                for query_type, stats in self.query_types_stats.items()
            }
    
    def get_recent_metrics(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get metrics from recent time period"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        with self._lock:
            recent_metrics = [
                m.to_dict() for m in self.metrics_history 
                if m.timestamp >= cutoff_time
            ]
            
            return recent_metrics
    
    def reset_stats(self) -> None:
        """Reset all statistics"""
        with self._lock:
            self.metrics_history.clear()
            self.stats = PerformanceStats()
            self.query_types_stats.clear()
        
        logger.info("Performance statistics reset")


def performance_monitor(monitor: PerformanceMonitor, query_type: str = "unknown"):
    """Decorator to monitor function performance"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            query_id = f"{func.__name__}_{int(time.time() * 1000)}"
            start_time = time.time()
            error = None
            result = None
            result_count = 0
            
            try:
                result = func(*args, **kwargs)
                if isinstance(result, (list, tuple)):
                    result_count = len(result)
                elif isinstance(result, dict):
                    result_count = len(result.get('results', []))
                else:
                    result_count = 1 if result else 0
                
                return result
            
            except Exception as e:
                error = str(e)
                raise
            
            finally:
                execution_time_ms = (time.time() - start_time) * 1000
                
                # Extract query text from args if available
                query_text = "N/A"
                if args and isinstance(args[0], str):
                    query_text = args[0]
                elif 'query' in kwargs:
                    query_text = kwargs['query']
                
                monitor.record_query(
                    query_id=query_id,
                    query_text=query_text,
                    query_type=query_type,
                    execution_time_ms=execution_time_ms,
                    result_count=result_count,
                    cache_hit=False,  # This would need to be determined by the function
                    error=error
                )
        
        return wrapper
    return decorator


class QueryProfiler:
    """Detailed query profiling for optimization"""
    
    def __init__(self):
        self.profiles: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self._lock = threading.Lock()
    
    def profile_query(self, query_text: str, execution_phases: Dict[str, float]) -> None:
        """Record detailed query execution phases"""
        profile = {
            'timestamp': datetime.now().isoformat(),
            'query_text': query_text[:100] + "..." if len(query_text) > 100 else query_text,
            'phases': execution_phases,
            'total_time_ms': sum(execution_phases.values())
        }
        
        with self._lock:
            self.profiles[query_text].append(profile)
            
            # Keep only last 100 profiles per query
            if len(self.profiles[query_text]) > 100:
                self.profiles[query_text] = self.profiles[query_text][-100:]
    
    def get_query_profile(self, query_text: str) -> List[Dict[str, Any]]:
        """Get profiling data for specific query"""
        with self._lock:
            return self.profiles.get(query_text, [])
    
    def get_optimization_suggestions(self) -> List[str]:
        """Generate optimization suggestions based on profiling data"""
        suggestions = []
        
        with self._lock:
            for query_text, profiles in self.profiles.items():
                if not profiles:
                    continue
                
                avg_total_time = sum(p['total_time_ms'] for p in profiles) / len(profiles)
                
                if avg_total_time > 1000:  # Slow query
                    # Analyze phases to find bottlenecks
                    avg_phases = defaultdict(float)
                    for profile in profiles:
                        for phase, time_ms in profile['phases'].items():
                            avg_phases[phase] += time_ms / len(profiles)
                    
                    slowest_phase = max(avg_phases.items(), key=lambda x: x[1])
                    
                    suggestions.append(
                        f"Query '{query_text[:50]}...' is slow ({avg_total_time:.1f}ms avg). "
                        f"Bottleneck: {slowest_phase[0]} ({slowest_phase[1]:.1f}ms)"
                    )
        
        return suggestions
