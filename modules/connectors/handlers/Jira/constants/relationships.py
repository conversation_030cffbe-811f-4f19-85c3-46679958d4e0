from enum import Enum

class RelationshipType(Enum):
    HAS_ISSUE = "HAS_ISSUE"
    ASSIGNED_TO = "ASSIGNED_TO"
    REPORTS = "REPORTS"
    RELATES_TO = "RELATES_TO"
    BLOCKS = "BLOCKS"
    DEPENDS_ON = "DEPENDS_ON"
    PART_OF_SPRINT = "PART_OF_SPRINT"
    HAS_COMPONENT = "HAS_COMPONENT"
    HAS_VERSION = "HAS_VERSION"

def get_all_relationship_types():
    return {r.value for r in RelationshipType}