# Enhanced Jira Connector

A comprehensive, secure, and high-performance Jira search connector with advanced query processing, caching, and monitoring capabilities.

## Features

### 🔒 Security
- **Query Sanitization**: Prevents SQL injection and XSS attacks
- **Input Validation**: Comprehensive validation of query parameters
- **Rate Limiting**: Configurable query rate limiting
- **Audit Logging**: Security event logging and monitoring

### 🚀 Performance
- **Intelligent Caching**: LRU cache with configurable TTL
- **Query Optimization**: Parameterized queries for better performance
- **Performance Monitoring**: Real-time metrics and slow query detection
- **Connection Pooling**: Efficient resource management

### 🧠 Advanced Query Processing
- **Natural Language Processing**: Intelligent query interpretation
- **Pattern Matching**: Advanced regex-based query parsing
- **Synonym Support**: Flexible keyword matching
- **Multi-type Queries**: Support for various search patterns

### 📊 Monitoring & Analytics
- **Performance Metrics**: Execution time, cache hit rates, error tracking
- **Query Profiling**: Detailed execution phase analysis
- **Statistics Dashboard**: Comprehensive performance insights
- **Optimization Suggestions**: Automated performance recommendations

## Architecture

```
JiraConnectorService
├── SearchConfig          # Configuration management
├── QuerySanitizer        # Security and validation
├── AdvancedQueryParser   # Natural language processing
├── SecureCypherBuilder   # Secure query construction
├── SearchCache           # Result caching
└── PerformanceMonitor    # Metrics and monitoring
```

## Quick Start

### Basic Usage

```python
from modules.connectors.handlers.Jira.service import JiraConnectorService

# Initialize with configuration
config = {
    "jira_url": "https://your-instance.atlassian.net",
    "username": "your-username",
    "password": "your-password",
    "max_results": 50,
    "enable_caching": True,
    "enable_query_logging": True
}

jira_service = JiraConnectorService(config)
jira_service.connect()

# Perform searches
results = jira_service.search("find issues with bug")
results = jira_service.search("issues assigned to Alice")
results = jira_service.search("show issues in project Alpha")
```

### Advanced Configuration

```python
from modules.connectors.handlers.Jira.config import JiraConnectorConfig

# Load from environment variables
config = JiraConnectorConfig.from_env()

# Or create custom configuration
config = JiraConnectorConfig(
    connection=JiraConnectionConfig(
        jira_url="https://your-instance.atlassian.net",
        username="api_user",
        password="secure_password",
        timeout_seconds=30
    ),
    search=SearchConfig(
        max_results=100,
        enable_caching=True,
        cache_ttl_seconds=600,
        enable_fuzzy_search=True
    ),
    security=SecurityConfig(
        enable_query_sanitization=True,
        enable_rate_limiting=True,
        max_queries_per_minute=120
    )
)

jira_service = JiraConnectorService(config.to_dict())
```

## Supported Query Types

### Issue Search
```python
# Keyword-based search
results = jira_service.search("find issues with bug")
results = jira_service.search("search for performance issues")

# With date filters
results = jira_service.search("issues updated yesterday")
results = jira_service.search("tickets from today")
results = jira_service.search("issues updated on 2025-06-29")

# With result limits
results = jira_service.search("show me 5 issues with login")
results = jira_service.search("limit 20 tickets about database")
```

### User Assignment Search
```python
results = jira_service.search("issues assigned to Alice")
results = jira_service.search("what did Bob work on")
results = jira_service.search("show tickets by user John")
```

### Project Search
```python
results = jira_service.search("issues in project Alpha")
results = jira_service.search("show me tickets from PROJ1")
results = jira_service.search("what's happening in Beta project")
```

### Relationship Search
```python
results = jira_service.search("what issues are blocking ISSUE-1")
results = jira_service.search("issues that depend on PROJ-123")
results = jira_service.search("show related tickets for BUG-456")
```

### Entity Listing
```python
results = jira_service.search("find all users")
results = jira_service.search("show all projects")
results = jira_service.search("list components")
results = jira_service.search("show sprints")
```

## Configuration

### Environment Variables

```bash
# Connection settings
export JIRA_URL="https://your-instance.atlassian.net"
export JIRA_USERNAME="your-username"
export JIRA_PASSWORD="your-password"
export JIRA_API_TOKEN="your-api-token"

# Search settings
export JIRA_SEARCH_MAX_RESULTS="50"
export JIRA_SEARCH_DEFAULT_LIMIT="10"
export JIRA_SEARCH_CACHE_TTL="300"
export JIRA_SEARCH_ENABLE_CACHE="true"
export JIRA_SEARCH_ENABLE_FUZZY="true"

# Security settings
export JIRA_SEC_ENABLE_SANITIZATION="true"
export JIRA_SEC_ENABLE_RATE_LIMIT="true"
export JIRA_SEC_MAX_QUERIES_PER_MIN="60"

# Performance settings
export JIRA_PERF_ENABLE_METRICS="true"
export JIRA_PERF_SLOW_QUERY_THRESHOLD="1.0"
```

### Configuration File

```json
{
  "connection": {
    "jira_url": "https://your-instance.atlassian.net",
    "username": "api_user",
    "password": "secure_password",
    "timeout_seconds": 30
  },
  "search": {
    "max_results": 50,
    "default_limit": 10,
    "cache_ttl_seconds": 300,
    "enable_caching": true,
    "enable_fuzzy_search": true
  },
  "security": {
    "enable_query_sanitization": true,
    "enable_rate_limiting": true,
    "max_queries_per_minute": 60
  },
  "performance": {
    "enable_metrics": true,
    "slow_query_threshold_seconds": 1.0
  }
}
```

## Monitoring & Analytics

### Performance Statistics

```python
# Get overall statistics
stats = jira_service.get_search_stats()
print(f"Cache hit rate: {stats['cache_hit_rate']}%")
print(f"Average execution time: {stats['avg_execution_time_ms']}ms")

# Get detailed performance metrics
from modules.connectors.handlers.Jira.performance import PerformanceMonitor

monitor = PerformanceMonitor()
slow_queries = monitor.get_slow_queries(limit=10)
query_breakdown = monitor.get_query_type_breakdown()
```

### Cache Management

```python
# Clear cache
jira_service.clear_cache()

# Check cache statistics
stats = jira_service.get_search_stats()
print(f"Cache size: {stats['cache_size']} entries")
```

## Security Features

### Query Sanitization
- Blocks SQL injection patterns
- Prevents XSS attacks
- Validates query length and format
- Logs security violations

### Rate Limiting
- Configurable queries per minute
- Per-user rate limiting
- Automatic throttling
- Rate limit violation logging

### Audit Logging
- All queries logged with timestamps
- Security events tracked
- Performance metrics recorded
- Error conditions monitored

## Testing

Run the comprehensive test suite:

```bash
cd modules/connectors/handlers/Jira
python test_enhanced_search.py
```

Test categories:
- **Security Tests**: Query sanitization, injection prevention
- **Performance Tests**: Caching, query optimization
- **Functionality Tests**: Query parsing, result accuracy
- **Integration Tests**: End-to-end search workflows

## Performance Optimization

### Best Practices
1. **Use specific keywords** for better query performance
2. **Limit result sets** with explicit limits
3. **Enable caching** for frequently used queries
4. **Monitor slow queries** and optimize patterns
5. **Use date filters** to reduce result sets

### Monitoring Slow Queries
```python
# Get slow query reports
slow_queries = monitor.get_slow_queries(limit=10)
for query in slow_queries:
    print(f"Query: {query['query_text']}")
    print(f"Time: {query['execution_time_ms']}ms")
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify Jira URL and credentials
   - Check network connectivity
   - Validate SSL certificates

2. **Slow Performance**
   - Enable caching
   - Use more specific queries
   - Check slow query logs

3. **Security Violations**
   - Review query sanitization logs
   - Check for dangerous patterns
   - Validate input sources

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed query logging
config['enable_query_logging'] = True
jira_service = JiraConnectorService(config)
```

## Contributing

1. Follow the existing code structure
2. Add comprehensive tests for new features
3. Update documentation
4. Ensure security best practices
5. Monitor performance impact

## License

This enhanced Jira connector is part of the organization service structure project.
