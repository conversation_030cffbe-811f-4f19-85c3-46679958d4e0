"""
Configuration management for Jira Connector
Provides centralized configuration with environment variable support and validation.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum


class LogLevel(Enum):
    """Logging levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


@dataclass
class JiraConnectionConfig:
    """Jira connection configuration"""
    jira_url: str
    username: str
    password: str
    api_token: Optional[str] = None
    timeout_seconds: int = 30
    max_retries: int = 3
    verify_ssl: bool = True
    
    @classmethod
    def from_env(cls) -> 'JiraConnectionConfig':
        """Create configuration from environment variables"""
        return cls(
            jira_url=os.getenv('JIRA_URL', ''),
            username=os.getenv('JIRA_USERNAME', ''),
            password=os.getenv('JIRA_PASSWORD', ''),
            api_token=os.getenv('JIRA_API_TOKEN'),
            timeout_seconds=int(os.getenv('JIRA_TIMEOUT', '30')),
            max_retries=int(os.getenv('JIRA_MAX_RETRIES', '3')),
            verify_ssl=os.getenv('JIRA_VERIFY_SSL', 'true').lower() == 'true'
        )


@dataclass
class SearchConfig:
    """Enhanced search configuration with validation"""
    max_results: int = 50
    default_limit: int = 10
    cache_ttl_seconds: int = 300
    enable_fuzzy_search: bool = True
    fuzzy_threshold: float = 0.8
    enable_caching: bool = True
    query_timeout_seconds: int = 30
    max_query_length: int = 1000
    enable_query_logging: bool = True
    log_level: LogLevel = LogLevel.INFO
    enable_performance_metrics: bool = True
    max_cache_size: int = 1000
    enable_query_validation: bool = True
    
    def __post_init__(self):
        """Validate configuration values"""
        if self.max_results <= 0 or self.max_results > 1000:
            raise ValueError("max_results must be between 1 and 1000")
        
        if self.default_limit <= 0 or self.default_limit > self.max_results:
            raise ValueError("default_limit must be between 1 and max_results")
        
        if self.cache_ttl_seconds < 0:
            raise ValueError("cache_ttl_seconds must be non-negative")
        
        if not 0.0 <= self.fuzzy_threshold <= 1.0:
            raise ValueError("fuzzy_threshold must be between 0.0 and 1.0")
        
        if self.query_timeout_seconds <= 0:
            raise ValueError("query_timeout_seconds must be positive")
        
        if self.max_query_length <= 0:
            raise ValueError("max_query_length must be positive")
    
    @classmethod
    def from_env(cls) -> 'SearchConfig':
        """Create search configuration from environment variables"""
        return cls(
            max_results=int(os.getenv('JIRA_SEARCH_MAX_RESULTS', '50')),
            default_limit=int(os.getenv('JIRA_SEARCH_DEFAULT_LIMIT', '10')),
            cache_ttl_seconds=int(os.getenv('JIRA_SEARCH_CACHE_TTL', '300')),
            enable_fuzzy_search=os.getenv('JIRA_SEARCH_ENABLE_FUZZY', 'true').lower() == 'true',
            fuzzy_threshold=float(os.getenv('JIRA_SEARCH_FUZZY_THRESHOLD', '0.8')),
            enable_caching=os.getenv('JIRA_SEARCH_ENABLE_CACHE', 'true').lower() == 'true',
            query_timeout_seconds=int(os.getenv('JIRA_SEARCH_TIMEOUT', '30')),
            max_query_length=int(os.getenv('JIRA_SEARCH_MAX_QUERY_LENGTH', '1000')),
            enable_query_logging=os.getenv('JIRA_SEARCH_ENABLE_LOGGING', 'true').lower() == 'true',
            log_level=LogLevel(os.getenv('JIRA_SEARCH_LOG_LEVEL', 'INFO')),
            enable_performance_metrics=os.getenv('JIRA_SEARCH_ENABLE_METRICS', 'true').lower() == 'true',
            max_cache_size=int(os.getenv('JIRA_SEARCH_MAX_CACHE_SIZE', '1000')),
            enable_query_validation=os.getenv('JIRA_SEARCH_ENABLE_VALIDATION', 'true').lower() == 'true'
        )


@dataclass
class PerformanceConfig:
    """Performance and monitoring configuration"""
    enable_metrics: bool = True
    metrics_retention_hours: int = 24
    slow_query_threshold_seconds: float = 1.0
    enable_query_profiling: bool = False
    max_concurrent_queries: int = 10
    
    @classmethod
    def from_env(cls) -> 'PerformanceConfig':
        """Create performance configuration from environment variables"""
        return cls(
            enable_metrics=os.getenv('JIRA_PERF_ENABLE_METRICS', 'true').lower() == 'true',
            metrics_retention_hours=int(os.getenv('JIRA_PERF_METRICS_RETENTION', '24')),
            slow_query_threshold_seconds=float(os.getenv('JIRA_PERF_SLOW_QUERY_THRESHOLD', '1.0')),
            enable_query_profiling=os.getenv('JIRA_PERF_ENABLE_PROFILING', 'false').lower() == 'true',
            max_concurrent_queries=int(os.getenv('JIRA_PERF_MAX_CONCURRENT', '10'))
        )


@dataclass
class SecurityConfig:
    """Security configuration"""
    enable_query_sanitization: bool = True
    enable_rate_limiting: bool = True
    max_queries_per_minute: int = 60
    enable_audit_logging: bool = True
    blocked_patterns: list = field(default_factory=lambda: [
        r'[;\'"\\]',
        r'(?i)(drop|delete|update|insert|create|alter|exec|execute)',
        r'(?i)(script|javascript|vbscript)',
        r'[<>]'
    ])
    
    @classmethod
    def from_env(cls) -> 'SecurityConfig':
        """Create security configuration from environment variables"""
        return cls(
            enable_query_sanitization=os.getenv('JIRA_SEC_ENABLE_SANITIZATION', 'true').lower() == 'true',
            enable_rate_limiting=os.getenv('JIRA_SEC_ENABLE_RATE_LIMIT', 'true').lower() == 'true',
            max_queries_per_minute=int(os.getenv('JIRA_SEC_MAX_QUERIES_PER_MIN', '60')),
            enable_audit_logging=os.getenv('JIRA_SEC_ENABLE_AUDIT', 'true').lower() == 'true'
        )


@dataclass
class JiraConnectorConfig:
    """Complete Jira connector configuration"""
    connection: JiraConnectionConfig
    search: SearchConfig
    performance: PerformanceConfig
    security: SecurityConfig
    
    @classmethod
    def from_env(cls) -> 'JiraConnectorConfig':
        """Create complete configuration from environment variables"""
        return cls(
            connection=JiraConnectionConfig.from_env(),
            search=SearchConfig.from_env(),
            performance=PerformanceConfig.from_env(),
            security=SecurityConfig.from_env()
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'JiraConnectorConfig':
        """Create configuration from dictionary"""
        return cls(
            connection=JiraConnectionConfig(**config_dict.get('connection', {})),
            search=SearchConfig(**config_dict.get('search', {})),
            performance=PerformanceConfig(**config_dict.get('performance', {})),
            security=SecurityConfig(**config_dict.get('security', {}))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'connection': self.connection.__dict__,
            'search': self.search.__dict__,
            'performance': self.performance.__dict__,
            'security': self.security.__dict__
        }
    
    def validate(self) -> None:
        """Validate complete configuration"""
        if not self.connection.jira_url:
            raise ValueError("Jira URL is required")
        
        if not self.connection.username:
            raise ValueError("Jira username is required")
        
        if not self.connection.password and not self.connection.api_token:
            raise ValueError("Either password or API token is required")


# Default configuration instance
DEFAULT_CONFIG = JiraConnectorConfig(
    connection=JiraConnectionConfig(
        jira_url="",
        username="",
        password=""
    ),
    search=SearchConfig(),
    performance=PerformanceConfig(),
    security=SecurityConfig()
)


def load_config(config_path: Optional[str] = None) -> JiraConnectorConfig:
    """
    Load configuration from various sources in order of precedence:
    1. Configuration file (if provided)
    2. Environment variables
    3. Default values
    """
    if config_path and os.path.exists(config_path):
        import json
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
        return JiraConnectorConfig.from_dict(config_dict)
    
    # Try to load from environment variables
    try:
        return JiraConnectorConfig.from_env()
    except Exception:
        # Fall back to default configuration
        return DEFAULT_CONFIG


def save_config(config: JiraConnectorConfig, config_path: str) -> None:
    """Save configuration to file"""
    import json
    with open(config_path, 'w') as f:
        json.dump(config.to_dict(), f, indent=2)
