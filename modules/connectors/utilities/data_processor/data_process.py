def process_content(path, chunk_size=1024*1024):
    # yields ~1 MB text chunks
    with open(path, "rb") as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            yield chunk.decode('utf-8', errors='ignore')


# def process_file(path):
#     for content_piece in process_content(path):
#         chunks = create_chunk(content_piece)       # process one piece at a time
#         embeds = create_embedding(chunks)
#         # ... store or stream out embeds immediately ...
