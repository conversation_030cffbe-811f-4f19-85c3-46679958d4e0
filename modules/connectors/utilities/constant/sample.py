from enum import Enum
from typing import Optional, List, Dict, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime


# -------------------------
# ENUM DEFINITIONS
# -------------------------

class EntityType(Enum):
    PERSON = "Person"
    EMPLOYEE = "Employee"
    MANAGER = "Manager"
    EXECUTIVE = "Executive"
    # ... include all others from original file
    ENTITY = "Entity"  # fallback


class RelationshipType(Enum):
    WORKS_FOR = "WORKS_FOR"
    MANAGES = "MANAGES"
    REPORTS_TO = "REPORTS_TO"
    LEADS = "LEADS"
    # ... include all others from original file
    INVOLVED_IN = "INVOLVED_IN"


# -------------------------
# ENTITY & RELATIONSHIP HELPERS
# -------------------------

def get_all_entity_types() -> Set[str]:
    return {et.value for et in EntityType}


def get_all_relationship_types() -> Set[str]:
    return {rt.value for rt in RelationshipType}


def is_valid_entity_type(entity_type: str) -> bool:
    return entity_type in get_all_entity_types()


def is_valid_relationship_type(rel_type: str) -> bool:
    return rel_type in get_all_relationship_types()


# -------------------------
# SCHEMA CLASSES
# -------------------------

@dataclass
class Entity:
    name: str
    entity_type: str
    description: Optional[str] = None
    aliases: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    properties: Optional[Dict] = None

    def __post_init__(self):
        if not self.name or not self.entity_type:
            raise ValueError("Entity must have name and type")
        self.name = self.name.strip()
        self.entity_type = self.entity_type.strip()


@dataclass
class EntityRelationship:
    subject: str
    predicate: str
    object: str
    subject_type: Optional[str] = None
    object_type: Optional[str] = None
    confidence_score: Optional[float] = None
    context: Optional[str] = None
    source_sentence: Optional[str] = None
    source_file_id: Optional[str] = None
    org_context: Optional[Dict[str, str]] = None
    extraction_timestamp: Optional[datetime] = None

    def __post_init__(self):
        if not self.subject or not self.predicate or not self.object:
            raise ValueError("subject, predicate, object are mandatory")
        self.subject = self.subject.strip()
        self.predicate = self.predicate.strip()
        self.object = self.object.strip()


@dataclass
class DocumentSummary:
    title: str
    summary: str
    document_type: Optional[str] = None
    key_topics: Optional[List[str]] = None
    language: Optional[str] = None
    confidence_score: Optional[float] = None


@dataclass
class ExtractedData:
    document_summary: DocumentSummary
    entities: List[Entity]
    relationships: List[EntityRelationship]
    extraction_timestamp: Optional[datetime] = None
    extraction_model: Optional[str] = None
    processing_time_seconds: Optional[float] = None


# -------------------------
# GRAPH NODES FOR STORAGE
# -------------------------

@dataclass
class GraphNode:
    node_id: str
    label: str
    name: str
    properties: dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None


@dataclass
class GraphRelationship:
    relationship_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str
    properties: dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None
    confidence_score: Optional[float] = None


# -------------------------
# VECTOR STORAGE DOCUMENT
# -------------------------

@dataclass
class VectorDocument:
    document_id: str
    text: str
    embedding: List[float]
    metadata: dict = field(default_factory=dict)

    def __post_init__(self):
        if not self.document_id or not self.text or not self.embedding:
            raise ValueError("VectorDocument must include id, text, and embedding")


# -------------------------
# COMMON PATTERNS & MAPPINGS (optional extensions)
# -------------------------

COMMON_ENTITY_RELATIONSHIP_PATTERNS: List[Tuple[str, str, str]] = [
    ("Employee", "REPORTS_TO", "Manager"),
    ("Team", "DEVELOPS", "Product"),
    ("Project", "TRACKS", "KPI"),
    # Add more patterns as needed
]
