from enum import Enum
from typing import Dict, TypedDict


class ConnectorCategory(str, Enum):
    DOCUMENTATION = "documentation"
    TASK_MANAGEMENT = "task_management"
    CODE_REPOSITORY = "code_repository"
    COMMUNICATION = "communication"
    EMAIL = "email"
    CALENDAR = "calendar"
    CRM = "crm"
    SPREADSHEET = "spreadsheet"
    BI_DASHBOARD = "bi_dashboard"
    DATABASE = "database"
    CLOUD_STORAGE = "cloud_storage"
    NOTE_TAKING = "note_taking"
    HR = "hr"
    FINANCE = "finance"
    MONITORING = "monitoring"
    AI_KG = "ai_kg"


class ConnectorStructure(str, Enum):
    STRUCTURED = "structured"
    UNSTRUCTURED = "unstructured"


class ConnectorInfo(TypedDict):
    category: ConnectorCategory
    structure: ConnectorStructure


TOOL_CATALOG: Dict[str, ConnectorInfo] = {
    # Documentation (mostly unstructured)
    "google_drive": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "confluence": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "dropbox_paper": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "onedrive": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "sharepoint": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "quip": {"category": ConnectorCategory.DOCUMENTATION, "structure": ConnectorStructure.UNSTRUCTURED},

    # Task Management (structured)
    "jira": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},
    "asana": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},
    "trello": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},
    "clickup": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},
    "monday": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},
    "linear": {"category": ConnectorCategory.TASK_MANAGEMENT, "structure": ConnectorStructure.STRUCTURED},

    # Code Repositories (structured)
    "github": {"category": ConnectorCategory.CODE_REPOSITORY, "structure": ConnectorStructure.STRUCTURED},
    "gitlab": {"category": ConnectorCategory.CODE_REPOSITORY, "structure": ConnectorStructure.STRUCTURED},
    "bitbucket": {"category": ConnectorCategory.CODE_REPOSITORY, "structure": ConnectorStructure.STRUCTURED},
    "azure_devops": {"category": ConnectorCategory.CODE_REPOSITORY, "structure": ConnectorStructure.STRUCTURED},

    # Communication (unstructured)
    "slack": {"category": ConnectorCategory.COMMUNICATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "microsoft_teams": {"category": ConnectorCategory.COMMUNICATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "discord": {"category": ConnectorCategory.COMMUNICATION, "structure": ConnectorStructure.UNSTRUCTURED},
    "google_chat": {"category": ConnectorCategory.COMMUNICATION, "structure": ConnectorStructure.UNSTRUCTURED},

    # Email (unstructured)
    "gmail": {"category": ConnectorCategory.EMAIL, "structure": ConnectorStructure.UNSTRUCTURED},
    "outlook": {"category": ConnectorCategory.EMAIL, "structure": ConnectorStructure.UNSTRUCTURED},

    # Calendar (structured)
    "google_calendar": {"category": ConnectorCategory.CALENDAR, "structure": ConnectorStructure.STRUCTURED},
    "outlook_calendar": {"category": ConnectorCategory.CALENDAR, "structure": ConnectorStructure.STRUCTURED},

    # CRM (structured)
    "salesforce": {"category": ConnectorCategory.CRM, "structure": ConnectorStructure.STRUCTURED},
    "hubspot": {"category": ConnectorCategory.CRM, "structure": ConnectorStructure.STRUCTURED},
    "zoho_crm": {"category": ConnectorCategory.CRM, "structure": ConnectorStructure.STRUCTURED},
    "pipedrive": {"category": ConnectorCategory.CRM, "structure": ConnectorStructure.STRUCTURED},
    "freshsales": {"category": ConnectorCategory.CRM, "structure": ConnectorStructure.STRUCTURED},

    # Spreadsheet (structured)
    "google_sheets": {"category": ConnectorCategory.SPREADSHEET, "structure": ConnectorStructure.STRUCTURED},
    "microsoft_excel": {"category": ConnectorCategory.SPREADSHEET, "structure": ConnectorStructure.STRUCTURED},
    "airtable": {"category": ConnectorCategory.SPREADSHEET, "structure": ConnectorStructure.STRUCTURED},

    # BI (structured)
    "tableau": {"category": ConnectorCategory.BI_DASHBOARD, "structure": ConnectorStructure.STRUCTURED},
    "power_bi": {"category": ConnectorCategory.BI_DASHBOARD, "structure": ConnectorStructure.STRUCTURED},
    "looker": {"category": ConnectorCategory.BI_DASHBOARD, "structure": ConnectorStructure.STRUCTURED},
    "metabase": {"category": ConnectorCategory.BI_DASHBOARD, "structure": ConnectorStructure.STRUCTURED},

    # Databases (structured)
    "postgresql": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "mysql": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "mongodb": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "snowflake": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "bigquery": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "redshift": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},
    "dynamodb": {"category": ConnectorCategory.DATABASE, "structure": ConnectorStructure.STRUCTURED},

    # Cloud Storage (mixed)
    "aws_s3": {"category": ConnectorCategory.CLOUD_STORAGE, "structure": ConnectorStructure.UNSTRUCTURED},
    "gcs": {"category": ConnectorCategory.CLOUD_STORAGE, "structure": ConnectorStructure.UNSTRUCTURED},
    "azure_blob": {"category": ConnectorCategory.CLOUD_STORAGE, "structure": ConnectorStructure.UNSTRUCTURED},

    # Notes (unstructured)
    "notion": {"category": ConnectorCategory.NOTE_TAKING, "structure": ConnectorStructure.UNSTRUCTURED},
    "evernote": {"category": ConnectorCategory.NOTE_TAKING, "structure": ConnectorStructure.UNSTRUCTURED},
    "obsidian": {"category": ConnectorCategory.NOTE_TAKING, "structure": ConnectorStructure.UNSTRUCTURED},

    # HR (structured)
    "bamboohr": {"category": ConnectorCategory.HR, "structure": ConnectorStructure.STRUCTURED},
    "workday": {"category": ConnectorCategory.HR, "structure": ConnectorStructure.STRUCTURED},
    "gusto": {"category": ConnectorCategory.HR, "structure": ConnectorStructure.STRUCTURED},
    "zenefits": {"category": ConnectorCategory.HR, "structure": ConnectorStructure.STRUCTURED},

    # Finance (structured)
    "quickbooks": {"category": ConnectorCategory.FINANCE, "structure": ConnectorStructure.STRUCTURED},
    "xero": {"category": ConnectorCategory.FINANCE, "structure": ConnectorStructure.STRUCTURED},
    "zoho_books": {"category": ConnectorCategory.FINANCE, "structure": ConnectorStructure.STRUCTURED},
    "freshbooks": {"category": ConnectorCategory.FINANCE, "structure": ConnectorStructure.STRUCTURED},

    # Monitoring (structured)
    "elasticsearch": {"category": ConnectorCategory.MONITORING, "structure": ConnectorStructure.STRUCTURED},
    "datadog": {"category": ConnectorCategory.MONITORING, "structure": ConnectorStructure.STRUCTURED},
    "sumo_logic": {"category": ConnectorCategory.MONITORING, "structure": ConnectorStructure.STRUCTURED},
    "logstash": {"category": ConnectorCategory.MONITORING, "structure": ConnectorStructure.STRUCTURED},

    # AI / KG (structured)
    "neo4j": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
    "weaviate": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
    "pinecone": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
    "langchain": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
    "dify": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
    "llamaindex": {"category": ConnectorCategory.AI_KG, "structure": ConnectorStructure.STRUCTURED},
}
