#!/usr/bin/env python3
"""
Comprehensive GitHub Connector Test Script

This script tests the complete GitHub connector functionality including:
- Connection establishment
- Data fetching and storage
- Knowledge graph operations
- Search functionality
- Performance metrics
"""

import os
import sys
import logging
import time
import json
from datetime import datetime
from typing import Dict, Any, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.service import GitHubConnectorService
from modules.connectors.utilities.constant.schemas import SearchStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('github_connector_test.log')
    ]
)

logger = logging.getLogger(__name__)

class GitHubConnectorTester:
    """Comprehensive tester for GitHub connector functionality"""
    
    def __init__(self):
        self.service = None
        self.test_results = {
            'connection': False,
            'data_fetch': False,
            'data_storage': False,
            'search_functionality': False,
            'performance_metrics': {},
            'errors': [],
            'statistics': {}
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all comprehensive tests for the GitHub connector.
        
        Returns:
            Dict: Complete test results
        """
        logger.info("=" * 60)
        logger.info("STARTING COMPREHENSIVE GITHUB CONNECTOR TESTS")
        logger.info("=" * 60)
        
        try:
            # Test 1: Environment and Connection
            self.test_environment_setup()
            self.test_connection()
            
            # Test 2: Data Operations
            self.test_data_fetching()
            self.test_data_storage()
            
            # Test 3: Search Functionality
            self.test_search_functionality()
            
            # Test 4: Performance and Statistics
            self.test_performance_metrics()
            self.test_statistics()
            
            # Test 5: Error Handling
            self.test_error_handling()
            
        except Exception as e:
            logger.error(f"Critical test failure: {str(e)}")
            self.test_results['errors'].append(f"Critical failure: {str(e)}")
        
        finally:
            self.cleanup()
        
        return self.generate_test_report()
    
    def test_environment_setup(self):
        """Test environment variables and dependencies"""
        logger.info("Testing environment setup...")
        
        required_env_vars = ['GITHUB_TOKEN', 'NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD']
        missing_vars = []
        
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            error_msg = f"Missing environment variables: {', '.join(missing_vars)}"
            logger.error(error_msg)
            self.test_results['errors'].append(error_msg)
            raise ValueError(error_msg)
        
        logger.info("✓ Environment setup complete")
    
    def test_connection(self):
        """Test GitHub API and Neo4j connections"""
        logger.info("Testing connections...")
        
        try:
            # Initialize service
            self.service = GitHubConnectorService()
            
            # Test connection
            start_time = time.time()
            connection = self.service.connect()
            connection_time = time.time() - start_time
            
            if connection and self.service._connected:
                self.test_results['connection'] = True
                self.test_results['performance_metrics']['connection_time'] = connection_time
                logger.info(f"✓ Connection established in {connection_time:.2f}s")
                
                # Test health check
                if hasattr(self.service.connection, 'health_check'):
                    is_healthy, health_info = self.service.connection.health_check()
                    logger.info(f"Health check: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")
                    self.test_results['health_info'] = health_info
            else:
                raise ConnectionError("Failed to establish connections")
                
        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            logger.error(error_msg)
            self.test_results['errors'].append(error_msg)
            raise
    
    def test_data_fetching(self):
        """Test data fetching from GitHub API"""
        logger.info("Testing data fetching...")
        
        try:
            start_time = time.time()
            fetched_data = []
            
            # Fetch data using the iterator
            for entity_data in self.service.fetch_data():
                fetched_data.append(entity_data)
                logger.debug(f"Fetched {entity_data.get('entity_type')}: {entity_data.get('id')}")
            
            fetch_time = time.time() - start_time
            
            if fetched_data:
                self.test_results['data_fetch'] = True
                self.test_results['performance_metrics']['fetch_time'] = fetch_time
                self.test_results['performance_metrics']['entities_fetched'] = len(fetched_data)
                
                # Analyze fetched data
                entity_counts = {}
                for entity in fetched_data:
                    entity_type = entity.get('entity_type', 'unknown')
                    entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
                
                self.test_results['statistics']['fetched_entities'] = entity_counts
                logger.info(f"✓ Fetched {len(fetched_data)} entities in {fetch_time:.2f}s")
                logger.info(f"Entity breakdown: {entity_counts}")
            else:
                raise ValueError("No data was fetched")
                
        except Exception as e:
            error_msg = f"Data fetching test failed: {str(e)}"
            logger.error(error_msg)
            self.test_results['errors'].append(error_msg)
    
    def test_data_storage(self):
        """Test data storage in Neo4j knowledge graph"""
        logger.info("Testing data storage...")
        
        try:
            if not self.test_results['data_fetch']:
                logger.warning("Skipping storage test - data fetch failed")
                return
            
            start_time = time.time()
            
            # Clear existing data for clean test
            if hasattr(self.service.neo4j_handler, 'clear_all_data'):
                self.service.neo4j_handler.clear_all_data()
            
            # Store data
            stored_count = 0
            for entity_data in self.service.fetch_data():
                try:
                    self.service.store_context(entity_data)
                    stored_count += 1
                except Exception as e:
                    logger.warning(f"Failed to store entity {entity_data.get('id')}: {str(e)}")
            
            storage_time = time.time() - start_time
            
            if stored_count > 0:
                self.test_results['data_storage'] = True
                self.test_results['performance_metrics']['storage_time'] = storage_time
                self.test_results['performance_metrics']['entities_stored'] = stored_count
                logger.info(f"✓ Stored {stored_count} entities in {storage_time:.2f}s")
            else:
                raise ValueError("No entities were stored")
                
        except Exception as e:
            error_msg = f"Data storage test failed: {str(e)}"
            logger.error(error_msg)
            self.test_results['errors'].append(error_msg)
    
    def test_search_functionality(self):
        """Test comprehensive search functionality"""
        logger.info("Testing search functionality...")
        
        try:
            if not self.test_results['data_storage']:
                logger.warning("Skipping search test - data storage failed")
                return
            
            # Test queries with different types
            test_queries = [
                ("python repositories", "entity"),
                ("repositories owned by", "relationship"),
                ("user issues", "hybrid"),
                ("javascript", "entity"),
                ("open issues", "entity")
            ]
            
            search_results = {}
            total_search_time = 0
            
            for query, search_type in test_queries:
                try:
                    start_time = time.time()
                    result = self.service.search(query, limit=5, search_type=search_type)
                    search_time = time.time() - start_time
                    total_search_time += search_time
                    
                    search_results[query] = {
                        'status': result.status.value,
                        'result_count': len(result.results),
                        'search_time': search_time,
                        'search_type': search_type
                    }
                    
                    logger.info(f"Query: '{query}' ({search_type}) -> {len(result.results)} results in {search_time:.3f}s")
                    
                    # Log sample results
                    for i, item in enumerate(result.results[:2]):
                        logger.debug(f"  Result {i+1}: {item.title} ({item.entity_type})")
                
                except Exception as e:
                    logger.error(f"Search failed for query '{query}': {str(e)}")
                    search_results[query] = {'error': str(e)}
            
            if search_results:
                self.test_results['search_functionality'] = True
                self.test_results['performance_metrics']['total_search_time'] = total_search_time
                self.test_results['search_results'] = search_results
                logger.info(f"✓ Search functionality tested - {len(search_results)} queries in {total_search_time:.2f}s")
            
        except Exception as e:
            error_msg = f"Search functionality test failed: {str(e)}"
            logger.error(error_msg)
            self.test_results['errors'].append(error_msg)
    
    def test_performance_metrics(self):
        """Test and collect performance metrics"""
        logger.info("Collecting performance metrics...")
        
        try:
            metrics = self.test_results['performance_metrics']
            
            # Calculate derived metrics
            if 'entities_fetched' in metrics and 'fetch_time' in metrics:
                metrics['fetch_rate'] = metrics['entities_fetched'] / metrics['fetch_time']
            
            if 'entities_stored' in metrics and 'storage_time' in metrics:
                metrics['storage_rate'] = metrics['entities_stored'] / metrics['storage_time']
            
            logger.info("✓ Performance metrics collected")
            
        except Exception as e:
            logger.warning(f"Performance metrics collection failed: {str(e)}")
    
    def test_statistics(self):
        """Test statistics collection from Neo4j"""
        logger.info("Testing statistics collection...")
        
        try:
            if hasattr(self.service.neo4j_handler, 'get_statistics'):
                stats = self.service.neo4j_handler.get_statistics()
                self.test_results['statistics']['neo4j_stats'] = stats
                logger.info(f"✓ Neo4j statistics: {stats}")
            
        except Exception as e:
            logger.warning(f"Statistics collection failed: {str(e)}")
    
    def test_error_handling(self):
        """Test error handling with invalid inputs"""
        logger.info("Testing error handling...")
        
        try:
            # Test invalid search queries
            invalid_queries = ["", "x" * 1001, None]
            
            for query in invalid_queries:
                try:
                    result = self.service.search(query, limit=5)
                    if result.status == SearchStatus.ERROR:
                        logger.debug(f"✓ Properly handled invalid query: {repr(query)}")
                    else:
                        logger.warning(f"Invalid query should have failed: {repr(query)}")
                except Exception as e:
                    logger.debug(f"✓ Exception properly raised for invalid query: {repr(query)}")
            
            logger.info("✓ Error handling tests completed")
            
        except Exception as e:
            logger.warning(f"Error handling test failed: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.service:
                if hasattr(self.service, 'connection') and self.service.connection:
                    self.service.connection.close()
                if hasattr(self.service, 'neo4j_handler') and self.service.neo4j_handler:
                    self.service.neo4j_handler.close()
            logger.info("✓ Cleanup completed")
        except Exception as e:
            logger.warning(f"Cleanup failed: {str(e)}")
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        logger.info("=" * 60)
        logger.info("GITHUB CONNECTOR TEST REPORT")
        logger.info("=" * 60)
        
        # Overall status
        overall_success = (
            self.test_results['connection'] and
            self.test_results['data_fetch'] and
            self.test_results['data_storage'] and
            self.test_results['search_functionality']
        )
        
        logger.info(f"Overall Status: {'✓ PASS' if overall_success else '✗ FAIL'}")
        logger.info(f"Connection: {'✓' if self.test_results['connection'] else '✗'}")
        logger.info(f"Data Fetch: {'✓' if self.test_results['data_fetch'] else '✗'}")
        logger.info(f"Data Storage: {'✓' if self.test_results['data_storage'] else '✗'}")
        logger.info(f"Search: {'✓' if self.test_results['search_functionality'] else '✗'}")
        
        # Performance metrics
        if self.test_results['performance_metrics']:
            logger.info("\nPerformance Metrics:")
            for key, value in self.test_results['performance_metrics'].items():
                if isinstance(value, float):
                    logger.info(f"  {key}: {value:.3f}")
                else:
                    logger.info(f"  {key}: {value}")
        
        # Errors
        if self.test_results['errors']:
            logger.info(f"\nErrors ({len(self.test_results['errors'])}):")
            for error in self.test_results['errors']:
                logger.info(f"  - {error}")
        
        # Add timestamp and overall status
        self.test_results['timestamp'] = datetime.now().isoformat()
        self.test_results['overall_success'] = overall_success
        
        logger.info("=" * 60)
        
        return self.test_results

def main():
    """Main test execution function"""
    tester = GitHubConnectorTester()
    results = tester.run_all_tests()
    
    # Save results to file
    with open('github_connector_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Exit with appropriate code
    exit_code = 0 if results['overall_success'] else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
