#!/usr/bin/env python3
"""
GitHub API Only Test Script

This script tests the GitHub connector functionality using only the GitHub API
when Neo4j is not available. It demonstrates the fallback search functionality.
"""

import os
import sys
import logging
import time
import json
from datetime import datetime
from typing import Dict, Any, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.service import GitHubConnectorService
from modules.connectors.utilities.constant.schemas import SearchStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class GitHubAPITester:
    """GitHub API only tester"""
    
    def __init__(self):
        self.service = None
        self.github_connected = False
    
    def test_github_api_connection(self):
        """Test GitHub API connection only"""
        logger.info("=" * 60)
        logger.info("TESTING GITHUB API CONNECTION")
        logger.info("=" * 60)
        
        try:
            # Initialize service
            self.service = GitHubConnectorService()
            
            # Test GitHub API connection only
            from modules.connectors.handlers.github.connection import GitHubConnection
            github_conn = GitHubConnection(self.service.config)
            
            if github_conn.connect():
                self.github_connected = True
                logger.info("✅ GitHub API connection successful!")
                
                # Test health check
                is_healthy, health_info = github_conn.health_check()
                logger.info(f"Health Status: {'✅ Healthy' if is_healthy else '❌ Unhealthy'}")
                logger.info(f"Authenticated User: {health_info.get('authenticated_user')}")
                logger.info(f"Rate Limit Remaining: {health_info.get('rate_limit_remaining')}")
                logger.info(f"API Status: {health_info.get('api_status')}")
                
                # Store connection for later use
                self.service.connection = github_conn
                return True
            else:
                logger.error("❌ GitHub API connection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection test failed: {str(e)}")
            return False
    
    def test_github_api_data_fetching(self):
        """Test fetching data from GitHub API"""
        logger.info("\n" + "=" * 60)
        logger.info("TESTING GITHUB API DATA FETCHING")
        logger.info("=" * 60)
        
        if not self.github_connected:
            logger.error("❌ Skipping - GitHub API not connected")
            return False
        
        try:
            # Test basic API calls
            logger.info("🔍 Testing repository search...")
            
            # Search for repositories
            response = self.service.connection.make_request('GET', 'search/repositories', params={
                'q': 'language:python stars:>1000',
                'sort': 'stars',
                'order': 'desc',
                'per_page': 5
            })
            
            if response.status_code == 200:
                data = response.json()
                repos = data.get('items', [])
                logger.info(f"✅ Found {len(repos)} repositories")
                
                for repo in repos[:3]:
                    logger.info(f"  📦 {repo['full_name']} - ⭐ {repo['stargazers_count']} stars")
                
                return True
            else:
                logger.error(f"❌ Repository search failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Data fetching test failed: {str(e)}")
            return False
    
    def test_github_api_search_fallback(self):
        """Test GitHub API search fallback functionality"""
        logger.info("\n" + "=" * 60)
        logger.info("TESTING GITHUB API SEARCH FALLBACK")
        logger.info("=" * 60)
        
        if not self.github_connected:
            logger.error("❌ Skipping - GitHub API not connected")
            return False
        
        try:
            # Test different search queries
            test_queries = [
                "python machine learning",
                "javascript react",
                "user:octocat",
                "language:python",
                "stars:>1000"
            ]
            
            search_results = {}
            
            for query in test_queries:
                logger.info(f"🔍 Testing query: '{query}'")
                
                try:
                    # Use the GitHub API search directly
                    response = self.service.connection.make_request('GET', 'search/repositories', params={
                        'q': query,
                        'sort': 'stars',
                        'order': 'desc',
                        'per_page': 3
                    })
                    
                    if response.status_code == 200:
                        data = response.json()
                        repos = data.get('items', [])
                        
                        search_results[query] = {
                            'status': 'success',
                            'count': len(repos),
                            'results': [
                                {
                                    'name': repo['full_name'],
                                    'description': repo.get('description', 'No description'),
                                    'stars': repo['stargazers_count'],
                                    'language': repo.get('language', 'Unknown')
                                }
                                for repo in repos
                            ]
                        }
                        
                        logger.info(f"  ✅ Found {len(repos)} results")
                        for repo in repos:
                            logger.info(f"    📦 {repo['full_name']} - {repo.get('language', 'Unknown')} - ⭐ {repo['stargazers_count']}")
                    else:
                        logger.warning(f"  ⚠️  Query failed: {response.status_code}")
                        search_results[query] = {'status': 'failed', 'error': response.status_code}
                
                except Exception as e:
                    logger.error(f"  ❌ Query error: {str(e)}")
                    search_results[query] = {'status': 'error', 'error': str(e)}
                
                time.sleep(1)  # Rate limiting
            
            # Summary
            successful_queries = sum(1 for result in search_results.values() if result.get('status') == 'success')
            logger.info(f"\n📊 Search Summary: {successful_queries}/{len(test_queries)} queries successful")
            
            return successful_queries > 0
            
        except Exception as e:
            logger.error(f"❌ Search fallback test failed: {str(e)}")
            return False
    
    def test_interactive_search(self):
        """Interactive search testing"""
        logger.info("\n" + "=" * 60)
        logger.info("INTERACTIVE GITHUB API SEARCH")
        logger.info("=" * 60)
        
        if not self.github_connected:
            logger.error("❌ GitHub API not connected")
            return
        
        print("\n🚀 Interactive GitHub Search (API Only)")
        print("Enter search queries to test GitHub API search functionality")
        print("Commands: 'quit' to exit, 'help' for examples")
        print()
        
        while True:
            try:
                query = input("🔍 Enter search query: ").strip()
                
                if not query:
                    continue
                elif query.lower() in ['quit', 'exit', 'q']:
                    break
                elif query.lower() == 'help':
                    self.show_search_examples()
                    continue
                
                # Execute search
                print(f"Searching for: '{query}'...")
                
                response = self.service.connection.make_request('GET', 'search/repositories', params={
                    'q': query,
                    'sort': 'stars',
                    'order': 'desc',
                    'per_page': 5
                })
                
                if response.status_code == 200:
                    data = response.json()
                    repos = data.get('items', [])
                    
                    if repos:
                        print(f"✅ Found {len(repos)} results:")
                        print()
                        
                        for i, repo in enumerate(repos, 1):
                            print(f"📦 Result {i}:")
                            print(f"   Name: {repo['full_name']}")
                            print(f"   Description: {repo.get('description', 'No description')}")
                            print(f"   Language: {repo.get('language', 'Unknown')}")
                            print(f"   Stars: ⭐ {repo['stargazers_count']}")
                            print(f"   URL: {repo['html_url']}")
                            print()
                    else:
                        print("📭 No results found")
                else:
                    print(f"❌ Search failed: HTTP {response.status_code}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def show_search_examples(self):
        """Show search examples"""
        print("\n📚 GitHub Search Examples:")
        print("  - python machine learning")
        print("  - javascript react")
        print("  - language:python stars:>1000")
        print("  - user:octocat")
        print("  - org:microsoft")
        print("  - created:>2023-01-01")
        print("  - size:>1000")
        print()
    
    def run_all_tests(self):
        """Run all tests"""
        logger.info("🔧 Starting GitHub API Tests...")
        
        results = {
            'connection': False,
            'data_fetching': False,
            'search_fallback': False
        }
        
        # Test connection
        results['connection'] = self.test_github_api_connection()
        
        # Test data fetching
        if results['connection']:
            results['data_fetching'] = self.test_github_api_data_fetching()
            results['search_fallback'] = self.test_github_api_search_fallback()
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Connection: {'✅ PASS' if results['connection'] else '❌ FAIL'}")
        logger.info(f"Data Fetching: {'✅ PASS' if results['data_fetching'] else '❌ FAIL'}")
        logger.info(f"Search Fallback: {'✅ PASS' if results['search_fallback'] else '❌ FAIL'}")
        
        overall_success = all(results.values())
        logger.info(f"Overall: {'✅ PASS' if overall_success else '❌ FAIL'}")
        
        # Offer interactive mode
        if results['connection']:
            print("\n🎯 Would you like to try interactive search? (y/n): ", end="")
            try:
                if input().lower().startswith('y'):
                    self.test_interactive_search()
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
        
        return results

def main():
    """Main function"""
    print("🔧 GitHub API Only Test Script")
    print("=" * 50)
    
    # Check environment
    if not os.getenv('GITHUB_TOKEN'):
        print("❌ Missing GITHUB_TOKEN environment variable")
        print("Please set this variable in your .env file")
        return
    
    # Run tests
    tester = GitHubAPITester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    exit_code = 0 if all(results.values()) else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
