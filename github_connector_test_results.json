{"connection": true, "data_fetch": true, "data_storage": true, "search_functionality": true, "performance_metrics": {"connection_time": 1.3198730945587158, "fetch_time": 2.6328089237213135, "entities_fetched": 3, "storage_time": 3.2882308959960938, "entities_stored": 3, "total_search_time": 3.552449941635132, "fetch_rate": 1.1394674231655537, "storage_rate": 0.9123446907736749}, "errors": [], "statistics": {"fetched_entities": {"repository": 2, "user": 1}, "neo4j_stats": {"GitHubRepository_count": 2, "GitHubUser_count": 1, "GitHubOrganization_count": 0, "GitHubIssue_count": 0, "total_relationships": 436}}, "health_info": {"timestamp": "2025-07-03T19:21:09.099464", "api_status": "none", "rate_limit_remaining": 4979, "rate_limit_reset": "2025-07-03T19:47:26", "authenticated_user": "arunimaray-alt", "permissions": [], "errors": []}, "search_results": {"python repositories": {"status": "success", "result_count": 5, "search_time": 0.6831839084625244, "search_type": "entity"}, "repositories owned by": {"status": "success", "result_count": 5, "search_time": 0.6648421287536621, "search_type": "relationship"}, "user issues": {"status": "success", "result_count": 5, "search_time": 0.7746438980102539, "search_type": "hybrid"}, "javascript": {"status": "success", "result_count": 5, "search_time": 0.6207249164581299, "search_type": "entity"}, "open issues": {"status": "success", "result_count": 5, "search_time": 0.8090550899505615, "search_type": "entity"}}, "timestamp": "2025-07-03T19:21:20.447998", "overall_success": true}