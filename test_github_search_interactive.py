#!/usr/bin/env python3
"""
Interactive GitHub Search Test Script

This script provides an interactive interface to test GitHub connector search functionality.
You can enter queries and see real-time search results.
"""

import os
import sys
import logging
from typing import List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.service import GitHubConnectorService
from modules.connectors.utilities.constant.schemas import SearchResultItem, SearchStatus

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise for interactive use

class GitHubSearchTester:
    """Interactive GitHub search tester"""
    
    def __init__(self):
        self.service = None
        self.connected = False
    
    def initialize(self) -> bool:
        """Initialize the GitHub connector service"""
        try:
            print("🔄 Initializing GitHub Connector...")
            self.service = GitHubConnectorService()
            
            print("🔄 Connecting to GitHub API and Neo4j...")
            connection = self.service.connect()
            
            if connection and self.service._connected:
                print("✅ Successfully connected to GitHub API and Neo4j!")
                self.connected = True
                
                # Optionally fetch and store some data
                self.setup_data()
                return True
            else:
                print("❌ Failed to connect to services")
                return False
                
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            return False
    
    def setup_data(self):
        """Fetch and store some GitHub data for testing"""
        try:
            print("🔄 Fetching and storing GitHub data for testing...")
            
            # Clear existing data
            if hasattr(self.service.neo4j_handler, 'clear_all_data'):
                self.service.neo4j_handler.clear_all_data()
            
            # Fetch and store data
            count = 0
            for entity_data in self.service.fetch_data():
                try:
                    self.service.store_context(entity_data)
                    count += 1
                    print(f"  📦 Stored {entity_data.get('entity_type')}: {entity_data.get('name', entity_data.get('title', 'Unknown'))}")
                except Exception as e:
                    print(f"  ⚠️  Failed to store entity: {str(e)}")
            
            print(f"✅ Setup complete! Stored {count} entities in knowledge graph.")
            
        except Exception as e:
            print(f"⚠️  Data setup failed: {str(e)}")
            print("You can still test search functionality with GitHub API fallback.")
    
    def search_and_display(self, query: str, search_type: str = "hybrid", limit: int = 10):
        """Execute search and display results"""
        if not self.connected:
            print("❌ Not connected to services")
            return
        
        try:
            print(f"\n🔍 Searching for: '{query}' (type: {search_type}, limit: {limit})")
            print("-" * 60)
            
            # Execute search
            result = self.service.search(query, limit=limit, search_type=search_type)
            
            # Display results
            if result.status == SearchStatus.SUCCESS:
                if result.results:
                    print(f"✅ Found {len(result.results)} results:")
                    print()
                    
                    for i, item in enumerate(result.results, 1):
                        print(f"📄 Result {i}:")
                        print(f"   Title: {item.title}")
                        print(f"   Type: {item.entity_type}")
                        print(f"   Summary: {item.summary}")
                        if item.url:
                            print(f"   URL: {item.url}")
                        if item.tags:
                            print(f"   Tags: {', '.join(item.tags)}")
                        print()
                else:
                    print("📭 No results found")
                
                # Display metrics
                if result.metrics:
                    print(f"⏱️  Search completed in {result.metrics.execution_time_ms:.1f}ms")
                
            elif result.status == SearchStatus.ERROR:
                print(f"❌ Search failed: {result.error.message if result.error else 'Unknown error'}")
            
        except Exception as e:
            print(f"❌ Search error: {str(e)}")
    
    def run_interactive_session(self):
        """Run interactive search session"""
        if not self.initialize():
            return
        
        print("\n" + "=" * 60)
        print("🚀 GITHUB CONNECTOR INTERACTIVE SEARCH")
        print("=" * 60)
        print()
        print("Available search types: entity, relationship, traversal, hybrid")
        print("Commands:")
        print("  - Enter a search query to search")
        print("  - 'type:SEARCH_TYPE' to change search type (e.g., 'type:entity')")
        print("  - 'limit:NUMBER' to change result limit (e.g., 'limit:5')")
        print("  - 'examples' to see example queries")
        print("  - 'stats' to see knowledge graph statistics")
        print("  - 'quit' or 'exit' to quit")
        print()
        
        search_type = "hybrid"
        limit = 10
        
        while True:
            try:
                user_input = input(f"🔍 Search ({search_type}, limit={limit}): ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'examples':
                    self.show_examples()
                elif user_input.lower() == 'stats':
                    self.show_statistics()
                elif user_input.startswith('type:'):
                    new_type = user_input[5:].strip()
                    if new_type in ['entity', 'relationship', 'traversal', 'hybrid']:
                        search_type = new_type
                        print(f"✅ Search type changed to: {search_type}")
                    else:
                        print("❌ Invalid search type. Use: entity, relationship, traversal, hybrid")
                elif user_input.startswith('limit:'):
                    try:
                        new_limit = int(user_input[6:].strip())
                        if 1 <= new_limit <= 50:
                            limit = new_limit
                            print(f"✅ Result limit changed to: {limit}")
                        else:
                            print("❌ Limit must be between 1 and 50")
                    except ValueError:
                        print("❌ Invalid limit. Use a number between 1 and 50")
                else:
                    # Execute search
                    self.search_and_display(user_input, search_type, limit)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        self.cleanup()
    
    def show_examples(self):
        """Show example search queries"""
        print("\n📚 Example Search Queries:")
        print("-" * 30)
        print("Entity searches:")
        print("  - python repositories")
        print("  - javascript projects")
        print("  - user octocat")
        print("  - open issues")
        print("  - bug reports")
        print()
        print("Relationship searches:")
        print("  - repositories owned by microsoft")
        print("  - issues created by user")
        print("  - projects by organization")
        print()
        print("Complex searches:")
        print("  - language:python machine learning")
        print("  - state:open high priority")
        print("  - user:torvalds linux")
        print()
    
    def show_statistics(self):
        """Show knowledge graph statistics"""
        try:
            if hasattr(self.service.neo4j_handler, 'get_statistics'):
                stats = self.service.neo4j_handler.get_statistics()
                print("\n📊 Knowledge Graph Statistics:")
                print("-" * 35)
                for key, value in stats.items():
                    print(f"  {key.replace('_', ' ').title()}: {value}")
                print()
            else:
                print("📊 Statistics not available")
        except Exception as e:
            print(f"❌ Failed to get statistics: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.service:
                if hasattr(self.service, 'connection') and self.service.connection:
                    self.service.connection.close()
                if hasattr(self.service, 'neo4j_handler') and self.service.neo4j_handler:
                    self.service.neo4j_handler.close()
            print("✅ Cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {str(e)}")

def main():
    """Main function"""
    print("🔧 GitHub Connector Interactive Search Test")
    print("=" * 50)
    
    # Check environment
    required_vars = ['GITHUB_TOKEN', 'NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables in your .env file")
        return
    
    # Run interactive session
    tester = GitHubSearchTester()
    tester.run_interactive_session()

if __name__ == "__main__":
    main()
