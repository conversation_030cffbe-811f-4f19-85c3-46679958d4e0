#!/usr/bin/env python3
"""
Test script for GitHub Connector Service
Tests the enhanced connect(), fetch_data(), and store_context() methods
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.service import GitHubConnectorService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_github_connector():
    """Test the GitHub connector with minimal data limits"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if required environment variables are set
    github_token = os.getenv('GITHUB_TOKEN')
    neo4j_uri = os.getenv('NEO4J_URI')
    neo4j_user = os.getenv('NEO4J_USER')
    neo4j_password = os.getenv('NEO4J_PASSWORD')
    
    if not github_token:
        logger.error("GITHUB_TOKEN not found in environment variables")
        return False
        
    if not all([neo4j_uri, neo4j_user, neo4j_password]):
        logger.warning("Neo4j credentials not found. Neo4j functionality will be limited.")
    
    try:
        # Initialize the GitHub connector
        logger.info("Initializing GitHub Connector Service...")
        github_service = GitHubConnectorService()
        
        # Test 1: Connect to GitHub API and Neo4j
        logger.info("Testing connect() method...")
        connection_result = github_service.connect()
        
        if connection_result.get('status') == 'success':
            logger.info("✅ Connection successful!")
            logger.info(f"GitHub API connected: {connection_result.get('github_connected', False)}")
            logger.info(f"Neo4j connected: {connection_result.get('neo4j_connected', False)}")
        else:
            logger.error("❌ Connection failed!")
            logger.error(f"Error: {connection_result.get('error', 'Unknown error')}")
            return False
        
        # Test 2: Fetch minimal data from GitHub
        logger.info("\nTesting fetch_data() method with minimal limits...")
        fetch_result = github_service.fetch_data()
        
        if fetch_result.get('status') == 'success':
            logger.info("✅ Data fetch successful!")
            data = fetch_result.get('data', {})
            logger.info(f"Repositories fetched: {len(data.get('repositories', []))}")
            logger.info(f"Users fetched: {len(data.get('users', []))}")
            logger.info(f"Organizations fetched: {len(data.get('organizations', []))}")
            logger.info(f"Issues fetched: {len(data.get('issues', []))}")
            logger.info(f"Relationships created: {len(data.get('relationships', []))}")
        else:
            logger.error("❌ Data fetch failed!")
            logger.error(f"Error: {fetch_result.get('error', 'Unknown error')}")
            return False
        
        # Test 3: Store context in Neo4j (if available)
        if neo4j_uri and neo4j_user and neo4j_password:
            logger.info("\nTesting store_context() method...")
            context_data = fetch_result.get('data', {})
            store_result = github_service.store_context(context_data)
            
            if store_result.get('status') == 'success':
                logger.info("✅ Context storage successful!")
                logger.info(f"Nodes created: {store_result.get('nodes_created', 0)}")
                logger.info(f"Relationships created: {store_result.get('relationships_created', 0)}")
            else:
                logger.error("❌ Context storage failed!")
                logger.error(f"Error: {store_result.get('error', 'Unknown error')}")
                return False
        else:
            logger.info("\nSkipping store_context() test - Neo4j credentials not available")
        
        logger.info("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        logger.exception("Full traceback:")
        return False

def main():
    """Main function to run the test"""
    logger.info("Starting GitHub Connector Test...")
    logger.info("=" * 50)
    
    success = test_github_connector()
    
    logger.info("=" * 50)
    if success:
        logger.info("✅ GitHub Connector Test PASSED")
        sys.exit(0)
    else:
        logger.error("❌ GitHub Connector Test FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()