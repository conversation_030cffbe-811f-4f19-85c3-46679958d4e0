# Unified Connector Architecture Plan

This document outlines the standardized architecture for creating and integrating new connectors into the system, based on the "Unified Connector Architecture Design" document.

## 1. Standardized Connector Directory Structure

Every new connector will be located within the `modules/connectors/handlers/` directory. The following structure must be used for any new connector (e.g., `my_connector`).

**Directory Structure:**

```
modules/connectors/handlers/
└── my_connector/
    ├── __init__.py
    ├── connection.py               # Connection logic, health checks
    ├── service.py                  # Main connector logic, inherits from BaseConnector
    ├── constants/                  # Connector-specific node and relationship definitions
    │   ├── __init__.py
    │   ├── entities.py
    │   └── relationships.py
    ├── schema.py                   # Data models and schemas
    ├── tests/
    │   └── test_my_connector.py    # Unit and integration tests
    └── connector_info.json         # Metadata for registration CLI
```

## 2. BaseConnector Contract

The `modules/connectors/base.py` file will define the abstract base class that all connectors must inherit from.

**`BaseConnector` Definition:**

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Iterator

class BaseConnector(ABC):
    """
    Defines the standard interface for all data source connectors
    as per the Unified Connector Architecture Design.
    """

    CONNECTOR_TYPE: str  # "structured" or "unstructured"

    @abstractmethod
    def connect(self) -> Any:
        """Establish and return client/connection"""
        pass

    @abstractmethod
    def get_connector(self) -> dict:
        """Returns metadata about the connector"""
        pass

    @abstractmethod
    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """Pulls all data, preferably with pagination as an iterator."""
        pass

    @abstractmethod
    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """Fetch a single entity by its ID."""
        pass

    @abstractmethod
    def sync(self):
        """Perform a full sync of the data source."""
        pass

    @abstractmethod
    def sync_by_id(self, id: str):
        """Perform a partial sync for a single entity."""
        pass

    @abstractmethod
    def store_context(self, data: Any):
        """Stores both context and embedding for given data."""
        pass

    @abstractmethod
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search for data within the source."""
        pass
```

## 3. Connector Registration Process

A CLI script, `register_connector.py`, will be used to register new connectors. This script reads metadata from `connector_info.json` and dynamically loads node and relationship definitions based on the connector type.

*   **For `unstructured` connectors:** It loads standard definitions from `modules/connectors/utilities/constant/`.
*   **For `structured` connectors:** It loads definitions from the connector's own `constants/` directory.

**Example `connector_info.json`:**

The `nodes` and `relationships` fields must always be empty arrays, as they are populated dynamically by the registration script.

```json
{
  "source_type": "jira",
  "name": "Jira",
  "connector_type": "structured",
  "category": "Task Management",
  "icon": "base64_encoded_icon_string",
  "description": "Jira is a project and issue tracking tool widely used by engineering, product, and QA teams to manage software development workflows, sprints, and bug tracking.",
  "purpose": "This connector integrates with Jira to fetch structured data about projects, issues (tasks, bugs, stories), comments, and attachments. It enables querying and analyzing task workflows, priorities, and developer activity by storing the data in a knowledge graph.",
  "nodes": [
    "Project", "Issue", "User", "Comment", "Attachment", "Sprint", "Status", "Priority"
  ],
  "relationships": [
    "ASSIGNED_TO", "BELONGS_TO_PROJECT", "HAS_COMMENT", "ATTACHED_TO", "HAS_STATUS", "IN_SPRINT", "CREATED_BY"
  ],
  "example_usage": "Useful for analyzing engineering productivity, sprint progress, bug severity trends, and ownership of tickets. Enables searching issues across all Jira projects in an organization using semantic or graph-based search.",
  "example_queries": [
    "Find all open bugs assigned to John Doe in the last sprint",
    "List tasks with high priority in Project Phoenix",
    "Which developer created the most issues last month?",
    "Show all stories linked to epic JIRA-2411",
    "What are the blockers in the current sprint for Team Alpha?",
    "Fetch all tasks that have been in 'In Progress' state for more than 5 days",
    "What bugs are frequently commented on or reopened?",
    "List all Jira issues modified in the last 7 days",
    "Which issues are assigned to users who are no longer active?",
    "Get a timeline of all tickets moved from 'To Do' to 'Done' in the past month"
  ]
}

```

## 4. Development and Registration Workflow

The following diagram illustrates the end-to-end process for adding a new connector.

**Workflow Diagram:**

```mermaid
graph TD
    A[Start: Read rules.md] --> B{Create Directory Structure};
    B --> C{Define Nodes & Relationships in constants/};
    C --> D[Implement Connector in service.py];
    D --> E{Implement all methods from BaseConnector};
    E --> F[Define metadata in connector_info.json];
    F --> G{Run `python modules/connectors/register_connector.py`};
    G --> H[Connector registered in Postgres/Pinecone];
    H --> I[Connector available via ConnectorFactory];
```

## 5. Documentation

A `rules.md` file will be created in the `modules/connectors/` directory to serve as the primary documentation for developers. It will contain detailed instructions for each step of the process.