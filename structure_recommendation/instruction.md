# 🧠 Unified Connector Architecture Design

## 📦 Core Modules

### 1. Database
- **Connection file**  
  - Logic to connect to the DB
  - Health check
- **Import in app**

---

## 📄 Queries & Scripts
- Centralized script file to contain all DB queries

---

## 🧭 Two Approaches to Search Flow

### ✅ Centralized Flow
- `main_search()` function receives query
- Analyze query → determine **category**
- Fetch **matching connectors** from registry
- If any connector is **unstructured**, invoke **semantic search**
- Store **usage examples** and descriptions in registry
- Use `fetch()` method of each connector
- Aggregate data and return final result

---

## 📁 Folder Structure Overview

```
Connectors/
│
├── Utils/
│   ├── data_processing.py         # File type processors: PDF, CSV, etc.
│   └── chunk_processing.py        # Chunking strategies: fixed-size, CDC, etc.

├── ConnectorA/
│   ├── connection.py
│   ├── service.py
│   ├── constants.py
│   └── schema.py

└── ConnectorB/
    └── ...
```

---

## 🧩 Connector Registry Design

### Registry Levels:
1. **Internal Registry**
   - Maintains available connectors
   - Stored in **Postgres** and **Pinecone**
   - Managed via **CLI**
2. **Organization-Specific Registry**
   - Lives under each **organization's config**
   - Tracks sources added by that org

---

## 🏢 Organization-Centric Flow

### 📥 Adding a Source
- Exposed via org service
- Inputs:
  - `org_id`, `source_type`, `creds`, `name`
- Steps:
  1. Check if `source_type` exists in internal registry
  2. Use `ConnectorFactory.get_connector_instance()`
  3. Call methods like `connect()`, `fetch()` on the instance
  4. Schedule `sync()` using instance

> Controlled via: `controller/service` file  
> Instances provided by: `factory method`  
> No enforced return structure from `sync()`

---

## 🔢 Connector Typing

### Connector Type
- **Structured**
- **Unstructured**

### Categories
- **Documentation**
- **Task Management**
- **Code Repository**
- *(All categories defined in code alongside connector type)*

---

## 🔄 Processing Flow

### For Unstructured Connectors

```
Sync Job Flow:
1. fetch_data()
2. For each entity (e.g. file):

   ├── preprocess_graph() → Build folder structure (org-level nodes)
   ├── process_content()   → Extract raw content
   ├── create_chunk()      → Chunk content
   ├── create_embedding()  → Generate embeddings
   ├── graph_extraction_unstructured() → Extract relationships
   └── graph_build()       → Persist knowledge graph
```

### For Structured Connectors

```
Sync Job Flow:
1. fetch_data() / fetch_data_byID()
2. process_graph() → Map source data to schema
   └── Optionally call chunk + embedding if needed
3. graph_build() → Store into KG
```

---

## 🧱 Base Connector Contract (base.py)

Every connector must define the following:

```
class BaseConnector:

    CONNECTOR_TYPE: str  # "structured" or "unstructured"

    def connect() -> Any:
        '''Establish and return client/connection'''

    def get_connector() -> dict:
        '''Returns metadata about the connector'''

    def fetch_data() -> List:
        '''Pulls data with pagination'''

    def fetch_data_byID(id: str) -> dict:
        '''Fetch a single entity'''

    def sync():
        '''Full sync of source'''

    def sync_byID(id: str):
        '''Partial sync (e.g., by file)'''

    def store_context():
        '''Stores both context and embedding'''

    def search(query: str) -> List:
        '''Search within the source'''
```