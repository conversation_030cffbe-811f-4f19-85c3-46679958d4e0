#!/usr/bin/env python3

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from modules.connectors.handlers.github.service import GitHubConnectorService

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Debug issue fetching specifically"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize GitHub service
    github_service = GitHubConnectorService()
    
    try:
        # Connect to services
        logger.info("Connecting to GitHub and Neo4j...")
        github_service.connect()
        
        # Clear existing data
        logger.info("Clearing existing GitHub data...")
        github_service.neo4j_handler.clear_all_data()
        
        # Debug: Test issue fetching directly
        logger.info("Testing issue fetching directly...")

        # Get repositories first
        repos = []
        for repo_data in github_service._fetch_repositories():
            repos.append(repo_data)
            logger.info(f"Found repository: {repo_data['github_id']} - {repo_data['full_name']}")

            # Test issue fetching for this repo
            logger.info(f"Fetching issues for repository {repo_data['github_id']}...")
            issue_count = 0
            for issue_data in github_service._fetch_issues_for_repository(repo_data['github_id']):
                logger.info(f"ISSUE FOUND: {issue_data['github_id']} - {issue_data['title']}")
                issue_count += 1

            logger.info(f"Total issues found for {repo_data['full_name']}: {issue_count}")

            # Also test the raw API call
            logger.info(f"Testing raw API call for {repo_data['full_name']}...")
            response = github_service.connection.make_request(
                'GET',
                f"/repos/{repo_data['full_name']}/issues?state=all&per_page=5"
            )
            if response.status_code == 200:
                raw_issues = response.json()
                logger.info(f"Raw API returned {len(raw_issues)} issues")
                for issue in raw_issues:
                    logger.info(f"  - Issue {issue['number']}: {issue['title']} (has pull_request: {'pull_request' in issue})")
            else:
                logger.error(f"Raw API call failed: {response.status_code}")

        # Now test the full fetch_data method
        logger.info("Testing full fetch_data method...")
        entities_stored = 0

        for entity_data in github_service.fetch_data():
            logger.info(f"Processing entity: {entity_data.get('entity_type')} - {entity_data.get('github_id')}")

            # Store the entity
            github_service.store_context(entity_data)
            entities_stored += 1

            # Debug: Print entity details if it's an issue
            if entity_data.get('entity_type') == 'issue':
                logger.info(f"ISSUE FOUND: {entity_data}")

        logger.info(f"Total entities processed: {entities_stored}")
        
        # Check what's actually in the database
        logger.info("Checking database contents...")
        stats = github_service.neo4j_handler.get_statistics()
        logger.info(f"Database stats: {stats}")
        
        # Try to query for issues specifically
        with github_service.neo4j_handler.get_session() as session:
            result = session.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
            for record in result:
                logger.info(f"Node type: {record['labels']} - Count: {record['count']}")
        
    except Exception as e:
        logger.error(f"Error during debug: {str(e)}")
        raise
    finally:
        # Cleanup
        if github_service:
            if hasattr(github_service, 'connection') and github_service.connection:
                github_service.connection.close()
            if hasattr(github_service, 'neo4j_handler') and github_service.neo4j_handler:
                github_service.neo4j_handler.close()

if __name__ == "__main__":
    main()
