#!/usr/bin/env python3
"""
Test script for GitHub Knowledge Graph Search
Tests the enhanced search functionality with various query types
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.service import GitHubConnectorService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_search_queries():
    """Test various search query types"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if required environment variables are set
    github_token = os.getenv('GITHUB_TOKEN')
    if not github_token:
        logger.error("GITHUB_TOKEN not found in environment variables")
        return False
    
    try:
        # Initialize the GitHub connector
        logger.info("Initializing GitHub Connector Service...")
        github_service = GitHubConnectorService()
        
        # Connect first
        logger.info("Connecting to GitHub API and Neo4j...")
        connection_result = github_service.connect()
        
        if connection_result.get('status') != 'success':
            logger.error("Failed to connect to GitHub services")
            return False
        
        # Test queries with different types
        test_queries = [
            # Entity-focused queries
            {
                "query": "python repositories",
                "type": "entity",
                "description": "Search for Python repositories"
            },
            {
                "query": "user octocat",
                "type": "entity", 
                "description": "Search for specific user"
            },
            {
                "query": "issues bug fix",
                "type": "entity",
                "description": "Search for bug fix issues"
            },
            
            # Relationship-focused queries
            {
                "query": "repositories owned by microsoft",
                "type": "relationship",
                "description": "Find repositories owned by Microsoft"
            },
            {
                "query": "issues created by torvalds",
                "type": "relationship", 
                "description": "Find issues created by specific user"
            },
            
            # Graph traversal queries
            {
                "query": "connection between react and facebook",
                "type": "traversal",
                "description": "Find connections between entities"
            },
            
            # Hybrid queries
            {
                "query": "machine learning projects with tensorflow",
                "type": "hybrid",
                "description": "Complex search across multiple entity types"
            },
            {
                "query": "open source javascript libraries",
                "type": "hybrid",
                "description": "General search with multiple criteria"
            }
        ]
        
        logger.info("=" * 60)
        logger.info("TESTING GITHUB KNOWLEDGE GRAPH SEARCH")
        logger.info("=" * 60)
        
        all_tests_passed = True
        
        for i, test_case in enumerate(test_queries, 1):
            logger.info(f"\nTest {i}: {test_case['description']}")
            logger.info(f"Query: '{test_case['query']}'")
            logger.info(f"Type: {test_case['type']}")
            logger.info("-" * 40)
            
            try:
                # Execute search
                search_response = github_service.search(
                    query=test_case['query'],
                    limit=5,
                    search_type=test_case['type']
                )
                
                # Analyze results
                if search_response.status.value == 'SUCCESS':
                    logger.info(f"✅ Search successful!")
                    logger.info(f"Results found: {search_response.total_count}")
                    logger.info(f"Execution time: {search_response.metrics.execution_time_ms:.2f}ms")
                    
                    # Display top results
                    for j, result in enumerate(search_response.results[:3], 1):
                        logger.info(f"  {j}. {result.title}")
                        logger.info(f"     Type: {result.entity_type}")
                        logger.info(f"     Summary: {result.summary[:100]}...")
                        if result.url:
                            logger.info(f"     URL: {result.url}")
                    
                    if search_response.total_count == 0:
                        logger.warning("⚠️  No results found - this might be expected for test data")
                    
                else:
                    logger.error(f"❌ Search failed!")
                    if search_response.error:
                        logger.error(f"Error: {search_response.error.message}")
                    all_tests_passed = False
                
            except Exception as e:
                logger.error(f"❌ Test failed with exception: {str(e)}")
                all_tests_passed = False
        
        # Performance test
        logger.info("\n" + "=" * 60)
        logger.info("PERFORMANCE TEST")
        logger.info("=" * 60)
        
        performance_queries = [
            "python",
            "javascript react",
            "machine learning",
            "open source",
            "web development"
        ]
        
        total_time = 0
        for query in performance_queries:
            try:
                response = github_service.search(query, limit=10)
                exec_time = response.metrics.execution_time_ms
                total_time += exec_time
                logger.info(f"Query: '{query}' - {exec_time:.2f}ms - {response.total_count} results")
            except Exception as e:
                logger.error(f"Performance test failed for '{query}': {str(e)}")
        
        avg_time = total_time / len(performance_queries)
        logger.info(f"\nAverage query time: {avg_time:.2f}ms")
        
        if avg_time < 1000:  # Less than 1 second
            logger.info("✅ Performance test passed - queries under 1 second")
        else:
            logger.warning("⚠️  Performance test warning - queries over 1 second")
        
        logger.info("\n" + "=" * 60)
        if all_tests_passed:
            logger.info("✅ ALL SEARCH TESTS PASSED!")
        else:
            logger.error("❌ SOME SEARCH TESTS FAILED!")
        logger.info("=" * 60)
        
        return all_tests_passed
        
    except Exception as e:
        logger.error(f"❌ Test suite failed with exception: {str(e)}")
        logger.exception("Full traceback:")
        return False

def test_search_edge_cases():
    """Test edge cases and error handling"""
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING EDGE CASES")
    logger.info("=" * 60)
    
    load_dotenv()
    github_token = os.getenv('GITHUB_TOKEN')
    
    if not github_token:
        logger.error("GITHUB_TOKEN not found")
        return False
    
    try:
        github_service = GitHubConnectorService()
        connection_result = github_service.connect()
        
        if connection_result.get('status') != 'success':
            logger.error("Failed to connect")
            return False
        
        edge_cases = [
            {
                "query": "",
                "description": "Empty query",
                "should_fail": True
            },
            {
                "query": "   ",
                "description": "Whitespace only query", 
                "should_fail": True
            },
            {
                "query": "a" * 1001,
                "description": "Query too long",
                "should_fail": True
            },
            {
                "query": "special!@#$%^&*()characters",
                "description": "Special characters",
                "should_fail": False
            },
            {
                "query": "unicode测试查询",
                "description": "Unicode characters",
                "should_fail": False
            }
        ]
        
        all_edge_tests_passed = True
        
        for i, test_case in enumerate(edge_cases, 1):
            logger.info(f"\nEdge Case {i}: {test_case['description']}")
            logger.info(f"Query: '{test_case['query'][:50]}{'...' if len(test_case['query']) > 50 else ''}'")
            
            try:
                response = github_service.search(test_case['query'], limit=5)
                
                if test_case['should_fail']:
                    if response.status.value == 'ERROR':
                        logger.info("✅ Correctly handled invalid query")
                    else:
                        logger.error("❌ Should have failed but didn't")
                        all_edge_tests_passed = False
                else:
                    if response.status.value == 'SUCCESS':
                        logger.info(f"✅ Handled edge case successfully - {response.total_count} results")
                    else:
                        logger.warning(f"⚠️  Edge case failed: {response.error.message if response.error else 'Unknown error'}")
                
            except Exception as e:
                if test_case['should_fail']:
                    logger.info(f"✅ Correctly threw exception: {str(e)}")
                else:
                    logger.error(f"❌ Unexpected exception: {str(e)}")
                    all_edge_tests_passed = False
        
        return all_edge_tests_passed
        
    except Exception as e:
        logger.error(f"Edge case testing failed: {str(e)}")
        return False

def main():
    """Main function to run all tests"""
    logger.info("Starting GitHub Knowledge Graph Search Tests...")
    logger.info("=" * 80)
    
    # Run main search tests
    search_tests_passed = test_search_queries()
    
    # Run edge case tests
    edge_tests_passed = test_search_edge_cases()
    
    # Final results
    logger.info("\n" + "=" * 80)
    logger.info("FINAL TEST RESULTS")
    logger.info("=" * 80)
    
    if search_tests_passed and edge_tests_passed:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("GitHub Knowledge Graph Search is working correctly!")
        sys.exit(0)
    else:
        logger.error("❌ SOME TESTS FAILED!")
        if not search_tests_passed:
            logger.error("- Search functionality tests failed")
        if not edge_tests_passed:
            logger.error("- Edge case tests failed")
        sys.exit(1)

if __name__ == "__main__":
    main()