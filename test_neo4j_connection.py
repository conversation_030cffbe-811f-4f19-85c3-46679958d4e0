#!/usr/bin/env python3
"""
Neo4j Connection Test Script

This script tests the Neo4j connection using credentials from .env file
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.connectors.handlers.github.neo4j_handler import GitHubNeo4jHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_neo4j_connection():
    """Test Neo4j connection with current credentials"""
    logger.info("=" * 60)
    logger.info("TESTING NEO4J CONNECTION")
    logger.info("=" * 60)
    
    # Display connection details (without password)
    neo4j_uri = os.getenv('NEO4J_URI')
    neo4j_user = os.getenv('NEO4J_USER')
    neo4j_database = os.getenv('NEO4j_DATABASE', 'neo4j')
    
    logger.info(f"Neo4j URI: {neo4j_uri}")
    logger.info(f"Neo4j User: {neo4j_user}")
    logger.info(f"Neo4j Database: {neo4j_database}")
    
    try:
        # Initialize Neo4j handler
        neo4j_handler = GitHubNeo4jHandler()
        
        # Test connection
        logger.info("Attempting to connect to Neo4j...")
        if neo4j_handler.connect():
            logger.info("✅ Neo4j connection successful!")
            
            # Test basic query
            logger.info("Testing basic query...")
            result = neo4j_handler.execute_query("RETURN 'Hello Neo4j' as message")
            if result:
                logger.info(f"✅ Query successful: {result}")
            
            # Get database statistics
            logger.info("Getting database statistics...")
            stats = neo4j_handler.get_statistics()
            logger.info(f"Database stats: {stats}")
            
            # Test session
            logger.info("Testing session management...")
            session = neo4j_handler.get_session()
            if session:
                logger.info("✅ Session created successfully")
                session.close()
            
            neo4j_handler.close()
            return True
            
        else:
            logger.error("❌ Neo4j connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Neo4j connection error: {str(e)}")
        return False

def test_neo4j_with_different_ports():
    """Test Neo4j connection with different port configurations"""
    logger.info("\n" + "=" * 60)
    logger.info("TESTING DIFFERENT NEO4J CONFIGURATIONS")
    logger.info("=" * 60)
    
    base_uri = os.getenv('NEO4J_URI', 'neo4j+s://62238a67.databases.neo4j.io')
    user = os.getenv('NEO4J_USER', 'neo4j')
    password = os.getenv('NEO4J_PASSWORD')
    
    # Different URI configurations to try
    uri_variants = [
        base_uri,  # Original
        base_uri + ":7687",  # With explicit port
        base_uri.replace("neo4j+s://", "bolt+s://"),  # Bolt protocol
        base_uri.replace("neo4j+s://", "neo4j://"),  # Without SSL
        base_uri.replace("neo4j+s://", "bolt://") + ":7687",  # Bolt without SSL
    ]
    
    for i, uri in enumerate(uri_variants, 1):
        logger.info(f"\nTesting configuration {i}: {uri}")
        
        try:
            # Temporarily set environment variable
            original_uri = os.environ.get('NEO4J_URI')
            os.environ['NEO4J_URI'] = uri
            
            # Test connection
            neo4j_handler = GitHubNeo4jHandler()
            if neo4j_handler.connect():
                logger.info(f"✅ Configuration {i} successful!")
                
                # Quick test query
                result = neo4j_handler.execute_query("RETURN 1 as test")
                logger.info(f"Test query result: {result}")
                
                neo4j_handler.close()
                
                # Restore original URI and return success
                if original_uri:
                    os.environ['NEO4J_URI'] = original_uri
                return True
            else:
                logger.warning(f"⚠️  Configuration {i} failed")
                
        except Exception as e:
            logger.warning(f"⚠️  Configuration {i} error: {str(e)}")
        finally:
            # Restore original URI
            if original_uri:
                os.environ['NEO4J_URI'] = original_uri
    
    logger.error("❌ All Neo4j configurations failed")
    return False

def main():
    """Main function"""
    logger.info("🔧 Neo4j Connection Test")
    logger.info("=" * 50)
    
    # Check environment variables
    required_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    # Test standard connection
    success = test_neo4j_connection()
    
    # If standard connection fails, try different configurations
    if not success:
        logger.info("\nStandard connection failed, trying alternative configurations...")
        success = test_neo4j_with_different_ports()
    
    if success:
        logger.info("\n🎉 Neo4j connection test completed successfully!")
    else:
        logger.error("\n💥 Neo4j connection test failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
