2025-07-03 18:46:18,936 - __main__ - INFO - ============================================================
2025-07-03 18:46:18,936 - __main__ - INFO - STARTING COMPREHENSIVE GITHUB CONNECTOR TESTS
2025-07-03 18:46:18,936 - __main__ - INFO - ============================================================
2025-07-03 18:46:18,936 - __main__ - INFO - Testing environment setup...
2025-07-03 18:46:18,936 - __main__ - ERROR - Missing environment variables: GITHUB_TOKEN, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
2025-07-03 18:46:18,936 - __main__ - ERROR - Critical test failure: Missing environment variables: GITHUB_TOKEN, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
2025-07-03 18:46:18,936 - __main__ - INFO - ✓ Cleanup completed
2025-07-03 18:46:18,936 - __main__ - INFO - ============================================================
2025-07-03 18:46:18,936 - __main__ - INFO - GITHUB CONNECTOR TEST REPORT
2025-07-03 18:46:18,936 - __main__ - INFO - ============================================================
2025-07-03 18:46:18,936 - __main__ - INFO - Overall Status: ✗ FAIL
2025-07-03 18:46:18,936 - __main__ - INFO - Connection: ✗
2025-07-03 18:46:18,937 - __main__ - INFO - Data Fetch: ✗
2025-07-03 18:46:18,937 - __main__ - INFO - Data Storage: ✗
2025-07-03 18:46:18,937 - __main__ - INFO - Search: ✗
2025-07-03 18:46:18,937 - __main__ - INFO - 
Errors (2):
2025-07-03 18:46:18,937 - __main__ - INFO -   - Missing environment variables: GITHUB_TOKEN, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
2025-07-03 18:46:18,937 - __main__ - INFO -   - Critical failure: Missing environment variables: GITHUB_TOKEN, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
2025-07-03 18:46:18,937 - __main__ - INFO - ============================================================
2025-07-03 18:47:25,803 - __main__ - INFO - ============================================================
2025-07-03 18:47:25,803 - __main__ - INFO - STARTING COMPREHENSIVE GITHUB CONNECTOR TESTS
2025-07-03 18:47:25,803 - __main__ - INFO - ============================================================
2025-07-03 18:47:25,803 - __main__ - INFO - Testing environment setup...
2025-07-03 18:47:25,803 - __main__ - INFO - ✓ Environment setup complete
2025-07-03 18:47:25,803 - __main__ - INFO - Testing connections...
2025-07-03 18:47:25,803 - modules.connectors.handlers.github.service - INFO - Initialized GitHub connector v1.0.0
2025-07-03 18:47:25,803 - modules.connectors.handlers.github.service - INFO - Connecting to GitHub API and Neo4j database...
2025-07-03 18:47:25,804 - modules.connectors.handlers.github.connection - INFO - Establishing connection to GitHub API...
2025-07-03 18:47:26,266 - modules.connectors.handlers.github.connection - INFO - Successfully connected to GitHub API as user: arunimaray-alt
2025-07-03 18:47:26,266 - modules.connectors.handlers.github.neo4j_handler - INFO - Connecting to Neo4j database...
2025-07-03 18:47:26,353 - modules.connectors.handlers.github.neo4j_handler - ERROR - Failed to connect to Neo4j: Cannot resolve address 62238a67.databases.neo4j.io:7687
2025-07-03 18:47:26,354 - modules.connectors.handlers.github.service - ERROR - Connection failed: Failed to establish Neo4j connection
2025-07-03 18:47:26,354 - modules.connectors.handlers.github.connection - INFO - GitHub API connection closed
2025-07-03 18:47:26,354 - modules.connectors.handlers.github.neo4j_handler - INFO - Neo4j connection closed
2025-07-03 18:47:26,354 - __main__ - ERROR - Connection test failed: Failed to establish Neo4j connection
2025-07-03 18:47:26,354 - __main__ - ERROR - Critical test failure: Failed to establish Neo4j connection
2025-07-03 18:47:26,354 - modules.connectors.handlers.github.connection - INFO - GitHub API connection closed
2025-07-03 18:47:26,354 - modules.connectors.handlers.github.neo4j_handler - INFO - Neo4j connection closed
2025-07-03 18:47:26,354 - __main__ - INFO - ✓ Cleanup completed
2025-07-03 18:47:26,354 - __main__ - INFO - ============================================================
2025-07-03 18:47:26,354 - __main__ - INFO - GITHUB CONNECTOR TEST REPORT
2025-07-03 18:47:26,354 - __main__ - INFO - ============================================================
2025-07-03 18:47:26,354 - __main__ - INFO - Overall Status: ✗ FAIL
2025-07-03 18:47:26,354 - __main__ - INFO - Connection: ✗
2025-07-03 18:47:26,354 - __main__ - INFO - Data Fetch: ✗
2025-07-03 18:47:26,354 - __main__ - INFO - Data Storage: ✗
2025-07-03 18:47:26,354 - __main__ - INFO - Search: ✗
2025-07-03 18:47:26,354 - __main__ - INFO - 
Errors (2):
2025-07-03 18:47:26,354 - __main__ - INFO -   - Connection test failed: Failed to establish Neo4j connection
2025-07-03 18:47:26,354 - __main__ - INFO -   - Critical failure: Failed to establish Neo4j connection
2025-07-03 18:47:26,354 - __main__ - INFO - ============================================================
2025-07-03 19:14:21,620 - __main__ - INFO - ============================================================
2025-07-03 19:14:21,621 - __main__ - INFO - STARTING COMPREHENSIVE GITHUB CONNECTOR TESTS
2025-07-03 19:14:21,621 - __main__ - INFO - ============================================================
2025-07-03 19:14:21,621 - __main__ - INFO - Testing environment setup...
2025-07-03 19:14:21,621 - __main__ - INFO - ✓ Environment setup complete
2025-07-03 19:14:21,621 - __main__ - INFO - Testing connections...
2025-07-03 19:14:21,621 - modules.connectors.handlers.github.service - INFO - Initialized GitHub connector v1.0.0
2025-07-03 19:14:21,621 - modules.connectors.handlers.github.service - INFO - Connecting to GitHub API and Neo4j database...
2025-07-03 19:14:21,621 - modules.connectors.handlers.github.connection - INFO - Establishing connection to GitHub API...
2025-07-03 19:14:22,044 - modules.connectors.handlers.github.connection - INFO - Successfully connected to GitHub API as user: arunimaray-alt
2025-07-03 19:14:22,044 - modules.connectors.handlers.github.neo4j_handler - INFO - Connecting to Neo4j database...
2025-07-03 19:14:23,847 - modules.connectors.handlers.github.neo4j_handler - INFO - Successfully connected to Neo4j database
2025-07-03 19:14:23,853 - modules.connectors.handlers.github.service - INFO - Successfully connected to GitHub API and Neo4j database
2025-07-03 19:14:23,853 - __main__ - INFO - ✓ Connection established in 2.23s
2025-07-03 19:14:24,579 - __main__ - INFO - Health check: ✓ Healthy
2025-07-03 19:14:24,579 - __main__ - INFO - Testing data fetching...
2025-07-03 19:14:24,579 - modules.connectors.handlers.github.service - INFO - Starting GitHub data fetch with minimal limits...
2025-07-03 19:14:24,580 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 repositories...
2025-07-03 19:14:24,955 - modules.connectors.handlers.github.service - INFO - Fetching up to 3 users...
2025-07-03 19:14:25,245 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 organizations...
2025-07-03 19:14:25,505 - modules.connectors.handlers.github.service - INFO - Fetching up to 5 issues per repository...
2025-07-03 19:14:27,059 - modules.connectors.handlers.github.service - INFO - Completed GitHub data fetch with minimal limits
2025-07-03 19:14:27,059 - __main__ - INFO - ✓ Fetched 3 entities in 2.48s
2025-07-03 19:14:27,060 - __main__ - INFO - Entity breakdown: {'repository': 2, 'user': 1}
2025-07-03 19:14:27,060 - __main__ - INFO - Testing data storage...
2025-07-03 19:14:27,200 - modules.connectors.handlers.github.neo4j_handler - INFO - Cleared all GitHub data from Neo4j
2025-07-03 19:14:27,201 - modules.connectors.handlers.github.service - INFO - Starting GitHub data fetch with minimal limits...
2025-07-03 19:14:27,201 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 repositories...
2025-07-03 19:14:27,844 - modules.connectors.handlers.github.service - INFO - Successfully stored repository:970000834 in Neo4j knowledge graph
2025-07-03 19:14:28,112 - modules.connectors.handlers.github.service - INFO - Successfully stored repository:983519205 in Neo4j knowledge graph
2025-07-03 19:14:28,237 - modules.connectors.handlers.github.service - INFO - Fetching up to 3 users...
2025-07-03 19:14:28,679 - modules.connectors.handlers.github.service - INFO - Successfully stored user:208458933 in Neo4j knowledge graph
2025-07-03 19:14:28,680 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 organizations...
2025-07-03 19:14:28,956 - modules.connectors.handlers.github.service - INFO - Fetching up to 5 issues per repository...
2025-07-03 19:14:30,672 - modules.connectors.handlers.github.service - INFO - Completed GitHub data fetch with minimal limits
2025-07-03 19:14:30,672 - __main__ - INFO - ✓ Stored 3 entities in 3.61s
2025-07-03 19:14:30,672 - __main__ - INFO - Testing search functionality...
2025-07-03 19:14:30,673 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'python repositories' (type: entity, limit: 5)
2025-07-03 19:14:30,869 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 3, column: 93, offset: 145} for query: "\n                        MATCH (n0:GitHubRepository)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubRepository' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:30,869 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:14:31,461 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:14:31,462 - modules.connectors.handlers.github.service - INFO - Search completed in 789.08ms with 5 results
2025-07-03 19:14:31,462 - __main__ - INFO - Query: 'python repositories' (entity) -> 5 results in 0.789s
2025-07-03 19:14:31,462 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'repositories owned by' (type: relationship, limit: 5)
2025-07-03 19:14:31,658 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownRelationshipTypeWarning} {category: UNRECOGNIZED} {title: The provided relationship type is not in the database.} {description: One of the relationship types in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing relationship type is: OWNS_REPOSITORY)} {position: line: 2, column: 34, offset: 34} for query: "\n                    MATCH (a)-[r:OWNS_REPOSITORY]->(b)\n                    WHERE toLower(a.name) CONTAINS toLower($query_text) \n                       OR toLower(b.name) CONTAINS toLower($query_text)\n                       OR toLower(a.login) CONTAINS toLower($query_text)\n                       OR toLower(b.login) CONTAINS toLower($query_text)\n                    RETURN a, r, b, 'OWNS_REPOSITORY' as rel_type\n                    LIMIT 5\n                 UNION ALL \n                    MATCH (a)-[r:CREATED_BY]->(b)\n                    WHERE toLower(a.name) CONTAINS toLower($query_text) \n                       OR toLower(b.name) CONTAINS toLower($query_text)\n                       OR toLower(a.login) CONTAINS toLower($query_text)\n                       OR toLower(b.login) CONTAINS toLower($query_text)\n                    RETURN a, r, b, 'CREATED_BY' as rel_type\n                    LIMIT 5\n                "
2025-07-03 19:14:31,658 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:14:32,206 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:14:32,206 - modules.connectors.handlers.github.service - INFO - Search completed in 744.22ms with 5 results
2025-07-03 19:14:32,206 - __main__ - INFO - Query: 'repositories owned by' (relationship) -> 5 results in 0.744s
2025-07-03 19:14:32,206 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'user issues' (type: hybrid, limit: 5)
2025-07-03 19:14:32,386 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 3, column: 93, offset: 139} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.name) CONTAINS toLower($query_text) OR toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.login) CONTAINS toLower($query_text) OR toLower(n1.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:32,387 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 8, column: 35, offset: 506} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.name) CONTAINS toLower($query_text) OR toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.login) CONTAINS toLower($query_text) OR toLower(n1.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:32,388 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 9, column: 93, offset: 611} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.name) CONTAINS toLower($query_text) OR toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.login) CONTAINS toLower($query_text) OR toLower(n1.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:32,389 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:14:32,860 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:14:32,860 - modules.connectors.handlers.github.service - INFO - Search completed in 654.05ms with 5 results
2025-07-03 19:14:32,860 - __main__ - INFO - Query: 'user issues' (hybrid) -> 5 results in 0.654s
2025-07-03 19:14:32,860 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'javascript' (type: entity, limit: 5)
2025-07-03 19:14:32,861 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:14:33,358 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:14:33,358 - modules.connectors.handlers.github.service - INFO - Search completed in 497.85ms with 5 results
2025-07-03 19:14:33,359 - __main__ - INFO - Query: 'javascript' (entity) -> 5 results in 0.498s
2025-07-03 19:14:33,359 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'open issues' (type: entity, limit: 5)
2025-07-03 19:14:33,913 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 2, column: 35, offset: 35} for query: "\n                        MATCH (n0:GitHubIssue)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:33,913 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 3, column: 93, offset: 140} for query: "\n                        MATCH (n0:GitHubIssue)\n                        WHERE (toLower(n0.name) CONTAINS toLower($query_text) OR toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.full_name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:14:33,914 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:14:34,476 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:14:34,476 - modules.connectors.handlers.github.service - INFO - Search completed in 1117.11ms with 5 results
2025-07-03 19:14:34,476 - __main__ - INFO - Query: 'open issues' (entity) -> 5 results in 1.117s
2025-07-03 19:14:34,476 - __main__ - INFO - ✓ Search functionality tested - 5 queries in 3.80s
2025-07-03 19:14:34,476 - __main__ - INFO - Collecting performance metrics...
2025-07-03 19:14:34,476 - __main__ - INFO - ✓ Performance metrics collected
2025-07-03 19:14:34,476 - __main__ - INFO - Testing statistics collection...
2025-07-03 19:14:34,777 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubOrganization)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (n:GitHubOrganization) RETURN count(n) as count'
2025-07-03 19:14:34,872 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (n:GitHubIssue) RETURN count(n) as count'
2025-07-03 19:14:34,971 - __main__ - INFO - ✓ Neo4j statistics: {'GitHubRepository_count': 2, 'GitHubUser_count': 1, 'GitHubOrganization_count': 0, 'GitHubIssue_count': 0, 'total_relationships': 436}
2025-07-03 19:14:34,971 - __main__ - INFO - Testing error handling...
2025-07-03 19:14:34,971 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: '' (type: hybrid, limit: 5)
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query cannot be empty
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' (type: hybrid, limit: 5)
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query too long (max 1000 characters)
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'None' (type: hybrid, limit: 5)
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query cannot be empty
2025-07-03 19:14:34,972 - __main__ - INFO - ✓ Error handling tests completed
2025-07-03 19:14:34,972 - modules.connectors.handlers.github.connection - INFO - GitHub API connection closed
2025-07-03 19:14:34,973 - modules.connectors.handlers.github.neo4j_handler - INFO - Neo4j connection closed
2025-07-03 19:14:34,973 - __main__ - INFO - ✓ Cleanup completed
2025-07-03 19:14:34,973 - __main__ - INFO - ============================================================
2025-07-03 19:14:34,973 - __main__ - INFO - GITHUB CONNECTOR TEST REPORT
2025-07-03 19:14:34,973 - __main__ - INFO - ============================================================
2025-07-03 19:14:34,973 - __main__ - INFO - Overall Status: ✓ PASS
2025-07-03 19:14:34,973 - __main__ - INFO - Connection: ✓
2025-07-03 19:14:34,973 - __main__ - INFO - Data Fetch: ✓
2025-07-03 19:14:34,973 - __main__ - INFO - Data Storage: ✓
2025-07-03 19:14:34,973 - __main__ - INFO - Search: ✓
2025-07-03 19:14:34,973 - __main__ - INFO - 
Performance Metrics:
2025-07-03 19:14:34,973 - __main__ - INFO -   connection_time: 2.232
2025-07-03 19:14:34,974 - __main__ - INFO -   fetch_time: 2.480
2025-07-03 19:14:34,974 - __main__ - INFO -   entities_fetched: 3
2025-07-03 19:14:34,974 - __main__ - INFO -   storage_time: 3.612
2025-07-03 19:14:34,974 - __main__ - INFO -   entities_stored: 3
2025-07-03 19:14:34,974 - __main__ - INFO -   total_search_time: 3.803
2025-07-03 19:14:34,974 - __main__ - INFO -   fetch_rate: 1.210
2025-07-03 19:14:34,974 - __main__ - INFO -   storage_rate: 0.830
2025-07-03 19:14:34,974 - __main__ - INFO - ============================================================
2025-07-03 19:21:07,778 - __main__ - INFO - ============================================================
2025-07-03 19:21:07,779 - __main__ - INFO - STARTING COMPREHENSIVE GITHUB CONNECTOR TESTS
2025-07-03 19:21:07,779 - __main__ - INFO - ============================================================
2025-07-03 19:21:07,779 - __main__ - INFO - Testing environment setup...
2025-07-03 19:21:07,779 - __main__ - INFO - ✓ Environment setup complete
2025-07-03 19:21:07,779 - __main__ - INFO - Testing connections...
2025-07-03 19:21:07,779 - modules.connectors.handlers.github.service - INFO - Initialized GitHub connector v1.0.0
2025-07-03 19:21:07,779 - modules.connectors.handlers.github.service - INFO - Connecting to GitHub API and Neo4j database...
2025-07-03 19:21:07,779 - modules.connectors.handlers.github.connection - INFO - Establishing connection to GitHub API...
2025-07-03 19:21:08,200 - modules.connectors.handlers.github.connection - INFO - Successfully connected to GitHub API as user: arunimaray-alt
2025-07-03 19:21:08,200 - modules.connectors.handlers.github.neo4j_handler - INFO - Connecting to Neo4j database...
2025-07-03 19:21:09,099 - modules.connectors.handlers.github.neo4j_handler - INFO - Successfully connected to Neo4j database
2025-07-03 19:21:09,099 - modules.connectors.handlers.github.service - INFO - Successfully connected to GitHub API and Neo4j database
2025-07-03 19:21:09,099 - __main__ - INFO - ✓ Connection established in 1.32s
2025-07-03 19:21:09,850 - __main__ - INFO - Health check: ✓ Healthy
2025-07-03 19:21:09,850 - __main__ - INFO - Testing data fetching...
2025-07-03 19:21:09,850 - modules.connectors.handlers.github.service - INFO - Starting GitHub data fetch with minimal limits...
2025-07-03 19:21:09,850 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 repositories...
2025-07-03 19:21:10,230 - modules.connectors.handlers.github.service - INFO - Fetching up to 3 users...
2025-07-03 19:21:10,515 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 organizations...
2025-07-03 19:21:10,846 - modules.connectors.handlers.github.service - INFO - Fetching up to 5 issues per repository...
2025-07-03 19:21:12,483 - modules.connectors.handlers.github.service - INFO - Completed GitHub data fetch with minimal limits
2025-07-03 19:21:12,483 - __main__ - INFO - ✓ Fetched 3 entities in 2.63s
2025-07-03 19:21:12,483 - __main__ - INFO - Entity breakdown: {'repository': 2, 'user': 1}
2025-07-03 19:21:12,483 - __main__ - INFO - Testing data storage...
2025-07-03 19:21:12,603 - modules.connectors.handlers.github.neo4j_handler - INFO - Cleared all GitHub data from Neo4j
2025-07-03 19:21:12,615 - modules.connectors.handlers.github.service - INFO - Starting GitHub data fetch with minimal limits...
2025-07-03 19:21:12,615 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 repositories...
2025-07-03 19:21:13,131 - modules.connectors.handlers.github.service - INFO - Successfully stored repository:970000834 in Neo4j knowledge graph
2025-07-03 19:21:13,387 - modules.connectors.handlers.github.service - INFO - Successfully stored repository:983519205 in Neo4j knowledge graph
2025-07-03 19:21:13,507 - modules.connectors.handlers.github.service - INFO - Fetching up to 3 users...
2025-07-03 19:21:13,934 - modules.connectors.handlers.github.service - INFO - Successfully stored user:208458933 in Neo4j knowledge graph
2025-07-03 19:21:13,935 - modules.connectors.handlers.github.service - INFO - Fetching up to 2 organizations...
2025-07-03 19:21:14,228 - modules.connectors.handlers.github.service - INFO - Fetching up to 5 issues per repository...
2025-07-03 19:21:15,771 - modules.connectors.handlers.github.service - INFO - Completed GitHub data fetch with minimal limits
2025-07-03 19:21:15,771 - __main__ - INFO - ✓ Stored 3 entities in 3.29s
2025-07-03 19:21:15,772 - __main__ - INFO - Testing search functionality...
2025-07-03 19:21:15,772 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'python repositories' (type: entity, limit: 5)
2025-07-03 19:21:15,905 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:21:16,455 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:21:16,455 - modules.connectors.handlers.github.service - INFO - Search completed in 683.07ms with 5 results
2025-07-03 19:21:16,455 - __main__ - INFO - Query: 'python repositories' (entity) -> 5 results in 0.683s
2025-07-03 19:21:16,455 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'repositories owned by' (type: relationship, limit: 5)
2025-07-03 19:21:16,608 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownRelationshipTypeWarning} {category: UNRECOGNIZED} {title: The provided relationship type is not in the database.} {description: One of the relationship types in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing relationship type is: OWNS_REPOSITORY)} {position: line: 2, column: 34, offset: 34} for query: "\n                    MATCH (a)-[r:OWNS_REPOSITORY]->(b)\n                    WHERE toLower(a.name) CONTAINS toLower($query_text) \n                       OR toLower(b.name) CONTAINS toLower($query_text)\n                       OR toLower(a.login) CONTAINS toLower($query_text)\n                       OR toLower(b.login) CONTAINS toLower($query_text)\n                    RETURN a, r, b, 'OWNS_REPOSITORY' as rel_type\n                    LIMIT 5\n                 UNION ALL \n                    MATCH (a)-[r:CREATED_BY]->(b)\n                    WHERE toLower(a.name) CONTAINS toLower($query_text) \n                       OR toLower(b.name) CONTAINS toLower($query_text)\n                       OR toLower(a.login) CONTAINS toLower($query_text)\n                       OR toLower(b.login) CONTAINS toLower($query_text)\n                    RETURN a, r, b, 'CREATED_BY' as rel_type\n                    LIMIT 5\n                "
2025-07-03 19:21:16,608 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:21:17,119 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:21:17,120 - modules.connectors.handlers.github.service - INFO - Search completed in 664.64ms with 5 results
2025-07-03 19:21:17,120 - __main__ - INFO - Query: 'repositories owned by' (relationship) -> 5 results in 0.665s
2025-07-03 19:21:17,120 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'user issues' (type: hybrid, limit: 5)
2025-07-03 19:21:17,328 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 8, column: 35, offset: 400} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:17,328 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 9, column: 43, offset: 455} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:17,329 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: body)} {position: line: 9, column: 94, offset: 506} for query: "\n                        MATCH (n0:GitHubUser)\n                        WHERE (toLower(n0.login) CONTAINS toLower($query_text) OR toLower(n0.name) CONTAINS toLower($query_text))\n                        RETURN 'GitHubUser' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                     UNION ALL \n                        MATCH (n1:GitHubIssue)\n                        WHERE (toLower(n1.title) CONTAINS toLower($query_text) OR toLower(n1.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n1 as entity\n                        ORDER BY n1.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:17,329 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:21:17,894 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:21:17,894 - modules.connectors.handlers.github.service - INFO - Search completed in 774.53ms with 5 results
2025-07-03 19:21:17,895 - __main__ - INFO - Query: 'user issues' (hybrid) -> 5 results in 0.775s
2025-07-03 19:21:17,895 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'javascript' (type: entity, limit: 5)
2025-07-03 19:21:17,895 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:21:18,515 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:21:18,515 - modules.connectors.handlers.github.service - INFO - Search completed in 620.62ms with 5 results
2025-07-03 19:21:18,515 - __main__ - INFO - Query: 'javascript' (entity) -> 5 results in 0.621s
2025-07-03 19:21:18,515 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'open issues' (type: entity, limit: 5)
2025-07-03 19:21:18,655 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 2, column: 35, offset: 35} for query: "\n                        MATCH (n0:GitHubIssue)\n                        WHERE (toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:18,656 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: title)} {position: line: 3, column: 43, offset: 90} for query: "\n                        MATCH (n0:GitHubIssue)\n                        WHERE (toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:18,656 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: body)} {position: line: 3, column: 94, offset: 141} for query: "\n                        MATCH (n0:GitHubIssue)\n                        WHERE (toLower(n0.title) CONTAINS toLower($query_text) OR toLower(n0.body) CONTAINS toLower($query_text))\n                        RETURN 'GitHubIssue' as entity_type, n0 as entity\n                        ORDER BY n0.updated_at DESC\n                        LIMIT 5\n                    "
2025-07-03 19:21:18,656 - modules.connectors.handlers.github.service - INFO - Knowledge graph search returned 0 results
2025-07-03 19:21:19,324 - modules.connectors.handlers.github.service - INFO - GitHub API search returned 5 results
2025-07-03 19:21:19,324 - modules.connectors.handlers.github.service - INFO - Search completed in 808.95ms with 5 results
2025-07-03 19:21:19,324 - __main__ - INFO - Query: 'open issues' (entity) -> 5 results in 0.809s
2025-07-03 19:21:19,325 - __main__ - INFO - ✓ Search functionality tested - 5 queries in 3.55s
2025-07-03 19:21:19,325 - __main__ - INFO - Collecting performance metrics...
2025-07-03 19:21:19,325 - __main__ - INFO - ✓ Performance metrics collected
2025-07-03 19:21:19,325 - __main__ - INFO - Testing statistics collection...
2025-07-03 19:21:20,238 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubOrganization)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (n:GitHubOrganization) RETURN count(n) as count'
2025-07-03 19:21:20,341 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: GitHubIssue)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (n:GitHubIssue) RETURN count(n) as count'
2025-07-03 19:21:20,445 - __main__ - INFO - ✓ Neo4j statistics: {'GitHubRepository_count': 2, 'GitHubUser_count': 1, 'GitHubOrganization_count': 0, 'GitHubIssue_count': 0, 'total_relationships': 436}
2025-07-03 19:21:20,445 - __main__ - INFO - Testing error handling...
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: '' (type: hybrid, limit: 5)
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query cannot be empty
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' (type: hybrid, limit: 5)
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query too long (max 1000 characters)
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - INFO - Starting GitHub search: 'None' (type: hybrid, limit: 5)
2025-07-03 19:21:20,445 - modules.connectors.handlers.github.service - ERROR - Search validation error: Query cannot be empty
2025-07-03 19:21:20,445 - __main__ - INFO - ✓ Error handling tests completed
2025-07-03 19:21:20,446 - modules.connectors.handlers.github.connection - INFO - GitHub API connection closed
2025-07-03 19:21:20,447 - modules.connectors.handlers.github.neo4j_handler - INFO - Neo4j connection closed
2025-07-03 19:21:20,447 - __main__ - INFO - ✓ Cleanup completed
2025-07-03 19:21:20,447 - __main__ - INFO - ============================================================
2025-07-03 19:21:20,447 - __main__ - INFO - GITHUB CONNECTOR TEST REPORT
2025-07-03 19:21:20,447 - __main__ - INFO - ============================================================
2025-07-03 19:21:20,447 - __main__ - INFO - Overall Status: ✓ PASS
2025-07-03 19:21:20,447 - __main__ - INFO - Connection: ✓
2025-07-03 19:21:20,447 - __main__ - INFO - Data Fetch: ✓
2025-07-03 19:21:20,447 - __main__ - INFO - Data Storage: ✓
2025-07-03 19:21:20,447 - __main__ - INFO - Search: ✓
2025-07-03 19:21:20,447 - __main__ - INFO - 
Performance Metrics:
2025-07-03 19:21:20,447 - __main__ - INFO -   connection_time: 1.320
2025-07-03 19:21:20,447 - __main__ - INFO -   fetch_time: 2.633
2025-07-03 19:21:20,447 - __main__ - INFO -   entities_fetched: 3
2025-07-03 19:21:20,447 - __main__ - INFO -   storage_time: 3.288
2025-07-03 19:21:20,447 - __main__ - INFO -   entities_stored: 3
2025-07-03 19:21:20,447 - __main__ - INFO -   total_search_time: 3.552
2025-07-03 19:21:20,447 - __main__ - INFO -   fetch_rate: 1.139
2025-07-03 19:21:20,447 - __main__ - INFO -   storage_rate: 0.912
2025-07-03 19:21:20,448 - __main__ - INFO - ============================================================
